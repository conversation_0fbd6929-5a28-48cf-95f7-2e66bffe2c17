"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PatientVaccinationsController = void 0;
const common_1 = require("@nestjs/common");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const patient_vaccinations_service_1 = require("./patient-vaccinations.service");
const swagger_1 = require("@nestjs/swagger");
const api_documentation_base_1 = require("../base/api-documentation-base");
const create_patient_vaccination_dto_1 = require("./dto/create-patient-vaccination.dto");
const update_patient_vaccination_dto_1 = require("./dto/update-patient-vaccination.dto");
const track_method_decorator_1 = require("../utils/new-relic/decorators/track-method.decorator");
let PatientVaccinationsController = class PatientVaccinationsController extends api_documentation_base_1.ApiDocumentationBase {
    constructor(logger, patientVaccinationsService) {
        super();
        this.logger = logger;
        this.patientVaccinationsService = patientVaccinationsService;
    }
    async create(createPaitentVaccinationDto) {
        try {
            return await this.patientVaccinationsService.create(createPaitentVaccinationDto);
        }
        catch (error) {
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async get(patientId) {
        try {
            return await this.patientVaccinationsService.get(patientId);
        }
        catch (error) {
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async update(id, updatePatientVaccinationDto) {
        try {
            return await this.patientVaccinationsService.update(id, updatePatientVaccinationDto);
        }
        catch (error) {
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
};
exports.PatientVaccinationsController = PatientVaccinationsController;
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'create new patient-vaccinations',
        type: patient_vaccinations_service_1.PatientVaccinationsService
    }),
    (0, common_1.Post)(),
    (0, common_1.UsePipes)(new common_1.ValidationPipe()),
    (0, track_method_decorator_1.TrackMethod)('create-patient-vaccinations'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_patient_vaccination_dto_1.CreatePatientVaccinationDto]),
    __metadata("design:returntype", Promise)
], PatientVaccinationsController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'get patient-vaccinations',
        type: patient_vaccinations_service_1.PatientVaccinationsService
    }),
    (0, common_1.Get)(':patientId'),
    (0, track_method_decorator_1.TrackMethod)('get-patient-vaccinations'),
    __param(0, (0, common_1.Param)('patientId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PatientVaccinationsController.prototype, "get", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'update patient-vaccinations',
        type: 'PatientVaccinationsService'
    }),
    (0, common_1.Put)(':id'),
    (0, track_method_decorator_1.TrackMethod)('update-patient-vaccinations'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_patient_vaccination_dto_1.UpdatePatientVaccinationDto]),
    __metadata("design:returntype", Promise)
], PatientVaccinationsController.prototype, "update", null);
exports.PatientVaccinationsController = PatientVaccinationsController = __decorate([
    (0, swagger_1.ApiTags)('Patient-Vaccinations'),
    (0, common_1.Controller)('patient-vaccinations'),
    __metadata("design:paramtypes", [winston_logger_service_1.WinstonLogger,
        patient_vaccinations_service_1.PatientVaccinationsService])
], PatientVaccinationsController);
//# sourceMappingURL=patient-vaccinations.controller.js.map