"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatRoomController = void 0;
const common_1 = require("@nestjs/common");
const chat_room_service_1 = require("./chat-room.service");
const create_chat_room_dto_1 = require("./dto/create-chat-room.dto");
const swagger_1 = require("@nestjs/swagger");
const create_chat_message_dto_1 = require("./dto/create-chat-message.dto");
const update_chat_user_room_dto_1 = require("./dto/update-chat-user-room.dto");
const track_method_decorator_1 = require("../utils/new-relic/decorators/track-method.decorator");
let ChatRoomController = class ChatRoomController {
    constructor(chatRoomService) {
        this.chatRoomService = chatRoomService;
    }
    async create(createChatRoomDto) {
        return this.chatRoomService.create(createChatRoomDto);
    }
    async findAllForUser(userId) {
        return this.chatRoomService.findAllForUser(userId);
    }
    async sendMessage(createChatMessageDto) {
        return this.chatRoomService.sendMessage(createChatMessageDto);
    }
    async getChatRoomDetails(chatRoomId) {
        return this.chatRoomService.getChatRoomDetails(chatRoomId);
    }
    async updateChatRoom(id, updateChatRoomDto) {
        return this.chatRoomService.updateChatRoom(id, updateChatRoomDto);
    }
};
exports.ChatRoomController = ChatRoomController;
__decorate([
    (0, common_1.Post)(),
    (0, track_method_decorator_1.TrackMethod)('create-chat-room'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_chat_room_dto_1.CreateChatRoomDto]),
    __metadata("design:returntype", Promise)
], ChatRoomController.prototype, "create", null);
__decorate([
    (0, common_1.Get)('user/:userId'),
    (0, track_method_decorator_1.TrackMethod)('findAllForUser-chat-room'),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ChatRoomController.prototype, "findAllForUser", null);
__decorate([
    (0, common_1.Post)('message'),
    (0, track_method_decorator_1.TrackMethod)('sendMessage-chat-room'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_chat_message_dto_1.CreateChatMessageDto]),
    __metadata("design:returntype", Promise)
], ChatRoomController.prototype, "sendMessage", null);
__decorate([
    (0, common_1.Get)('/:id'),
    (0, track_method_decorator_1.TrackMethod)('getChatRoomDetails-chat-room'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ChatRoomController.prototype, "getChatRoomDetails", null);
__decorate([
    (0, common_1.Put)('/:id'),
    (0, track_method_decorator_1.TrackMethod)('updateChatRoom-chat-room'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_chat_user_room_dto_1.UpdateChatUserRoomDto]),
    __metadata("design:returntype", Promise)
], ChatRoomController.prototype, "updateChatRoom", null);
exports.ChatRoomController = ChatRoomController = __decorate([
    (0, swagger_1.ApiTags)('Chat Rooms'),
    (0, common_1.Controller)('chat-rooms'),
    __metadata("design:paramtypes", [chat_room_service_1.ChatRoomService])
], ChatRoomController);
//# sourceMappingURL=chat-room.controller.js.map