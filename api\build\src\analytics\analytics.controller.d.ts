import { Response } from 'express';
import { AnalyticsService } from './analytics.service';
import { DownloadAnalyticsReportDto, GetRevenueChartDataDto, RevenueChartDataPoint, CollectedPaymentsChartDataPoint, GetAppointmentsChartDataDto, AppointmentsChartResponse, DoctorSummaryResponseDto, GetDoctorSummaryDto, SummaryResponseDto, GetSummaryDto } from './dto/analytics.dto';
import { ShareAnalyticsDocumentsDto, ShareAnalyticsDocumentsResponseDto, AnalyticsDocumentStatusDto } from '../analytics-sharing/dto/share-analytics-documents.dto';
import { AnalyticsDocumentService } from '../analytics-sharing/services/analytics-document.service';
import { RequestWithUser } from '../auth/interfaces/request-with-user.interface';
export declare class AnalyticsController {
    private readonly analyticsService;
    private readonly analyticsDocumentService;
    constructor(analyticsService: AnalyticsService, analyticsDocumentService: AnalyticsDocumentService);
    getRevenueChartData(dto: GetRevenueChartDataDto): Promise<RevenueChartDataPoint[]>;
    getCollectedPaymentsChartData(dto: GetRevenueChartDataDto): Promise<CollectedPaymentsChartDataPoint[]>;
    getAppointmentsChartData(dto: GetAppointmentsChartDataDto): Promise<AppointmentsChartResponse>;
    downloadReport(dto: DownloadAnalyticsReportDto, res: Response): Promise<void>;
    getDoctorSummary(dto: GetDoctorSummaryDto): Promise<DoctorSummaryResponseDto[]>;
    getSummary(dto: GetSummaryDto): Promise<SummaryResponseDto>;
    shareAnalyticsDocuments(dto: ShareAnalyticsDocumentsDto, req: RequestWithUser): Promise<ShareAnalyticsDocumentsResponseDto>;
    getAnalyticsDocumentStatus(requestId: string): Promise<AnalyticsDocumentStatusDto>;
}
