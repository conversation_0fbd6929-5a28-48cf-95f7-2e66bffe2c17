"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClinicAlertsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const api_documentation_base_1 = require("../base/api-documentation-base");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const clinic_alerts_service_1 = require("./clinic-alerts.service");
const create_clinicAlerts_dto_1 = require("./dto/create-clinicAlerts.dto");
const update_clinicAlerts_dto_1 = require("./dto/update-clinicAlerts.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../roles/roles.decorator");
const role_enum_1 = require("../roles/role.enum");
const track_method_decorator_1 = require("../utils/new-relic/decorators/track-method.decorator");
let ClinicAlertsController = class ClinicAlertsController extends api_documentation_base_1.ApiDocumentationBase {
    constructor(logger, clinicAlertsService) {
        super();
        this.logger = logger;
        this.clinicAlertsService = clinicAlertsService;
    }
    async createClinicAlerts(createClinicAlertDto, req) {
        try {
            this.logger.log('creating clinic-alerts', {
                dto: createClinicAlertDto
            });
            return await this.clinicAlertsService.createClinicAlerts(createClinicAlertDto, req.user.brandId);
        }
        catch (error) {
            this.logger.error('failed to create Clinic-alerts', {
                error,
                createClinicAlertDto
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getClinicAlerts(clinicId, search) {
        try {
            this.logger.log('creating clinic-alerts', {
                id: clinicId
            });
            return await this.clinicAlertsService.getClinicAlerts(clinicId, search);
        }
        catch (error) {
            this.logger.error('failed to return Clinic-alerts', {
                error,
                clinicId
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async deleteClinicAlert(id) {
        try {
            this.logger.log('Delete the clinic-alert', { id });
            return await this.clinicAlertsService.deleteClinicAlert(id);
        }
        catch (error) {
            this.logger.error('Error removing clinic-alert by ID', { error });
            throw new common_1.HttpException('Error removing clinic-alert', common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async updateClinicAlert(id, updateClinicAlertDto) {
        const updatedClinicAlert = this.clinicAlertsService.updateClinicAlerts(id, updateClinicAlertDto);
        this.logger.log('clinic-alert updated successfully', {
            clinicAlertId: id
        });
        return updatedClinicAlert;
    }
};
exports.ClinicAlertsController = ClinicAlertsController;
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'create clinic alerts',
        type: clinic_alerts_service_1.ClinicAlertsService
    }),
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, common_1.UsePipes)(new common_1.ValidationPipe()),
    (0, track_method_decorator_1.TrackMethod)('createClinicAlerts-clinic-alerts'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_clinicAlerts_dto_1.CreateClinicAlertDto, Object]),
    __metadata("design:returntype", Promise)
], ClinicAlertsController.prototype, "createClinicAlerts", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'get-clinic alerts',
        type: clinic_alerts_service_1.ClinicAlertsService
    }),
    (0, common_1.Get)(':clinicId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiParam)({ name: 'clinicId', type: 'string' }),
    (0, swagger_1.ApiQuery)({ name: 'search', type: 'string', required: false }),
    (0, track_method_decorator_1.TrackMethod)('getClinicAlerts-clinic-alerts'),
    __param(0, (0, common_1.Param)('clinicId')),
    __param(1, (0, common_1.Query)('search')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ClinicAlertsController.prototype, "getClinicAlerts", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'delete-clinic alerts',
        type: clinic_alerts_service_1.ClinicAlertsService
    }),
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('deleteClinicAlert-clinic-alerts'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ClinicAlertsController.prototype, "deleteClinicAlert", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'delete-clinic alerts',
        type: clinic_alerts_service_1.ClinicAlertsService
    }),
    (0, common_1.Put)(':id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('updateClinicAlert-clinic-alerts'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_clinicAlerts_dto_1.UpdateClinicAlertsDto]),
    __metadata("design:returntype", Promise)
], ClinicAlertsController.prototype, "updateClinicAlert", null);
exports.ClinicAlertsController = ClinicAlertsController = __decorate([
    (0, swagger_1.ApiTags)('Clinic-alerts'),
    (0, common_1.Controller)('clinic-alerts'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    __metadata("design:paramtypes", [winston_logger_service_1.WinstonLogger,
        clinic_alerts_service_1.ClinicAlertsService])
], ClinicAlertsController);
//# sourceMappingURL=clinic-alerts.controller.js.map