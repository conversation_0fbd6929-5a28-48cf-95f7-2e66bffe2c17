"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../roles/roles.decorator");
const role_enum_1 = require("../roles/role.enum");
const analytics_service_1 = require("./analytics.service");
const analytics_dto_1 = require("./dto/analytics.dto");
const share_analytics_documents_dto_1 = require("../analytics-sharing/dto/share-analytics-documents.dto");
const analytics_document_service_1 = require("../analytics-sharing/services/analytics-document.service");
const uuidv7_1 = require("uuidv7");
const analytics_document_request_entity_1 = require("../analytics-sharing/entities/analytics-document-request.entity");
const track_method_decorator_1 = require("../utils/new-relic/decorators/track-method.decorator");
const users_service_1 = require("../users/users.service");
let AnalyticsController = class AnalyticsController {
    constructor(analyticsService, analyticsDocumentService, usersService) {
        this.analyticsService = analyticsService;
        this.analyticsDocumentService = analyticsDocumentService;
        this.usersService = usersService;
    }
    async getRevenueChartData(dto) {
        return await this.analyticsService.getRevenueChartData(dto);
    }
    async getCollectedPaymentsChartData(dto) {
        return await this.analyticsService.getCollectedPaymentsChartData(dto);
    }
    async getAppointmentsChartData(dto) {
        return await this.analyticsService.getAppointmentsChartData(dto);
    }
    async downloadReport(dto, res) {
        const buffer = await this.analyticsService.generateReport(dto);
        res.set({
            'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition': `attachment; filename="${dto.type}-report.xlsx"`,
            'Content-Length': buffer.length,
            'Cache-Control': 'no-cache'
        });
        res.end(buffer);
    }
    async getDoctorSummary(dto) {
        return await this.analyticsService.getDoctorSummary(dto);
    }
    async getSummary(dto) {
        return await this.analyticsService.getSummary(dto);
    }
    async shareAnalyticsDocuments(dto, req) {
        // Generate unique request ID
        const requestId = (0, uuidv7_1.uuidv4)();
        // Get user ID from JWT context
        const userId = req.user.userId || req.user.id;
        if (!userId) {
            throw new Error('User ID not found in JWT context');
        }
        // Determine recipient email based on type
        let recipientEmail = dto.recipientEmail;
        if (dto.recipientType === analytics_document_request_entity_1.AnalyticsRecipientType.CLIENT) {
            // For CLIENT type, automatically use the current user's email
            const user = await this.usersService.findOne(userId);
            recipientEmail = user.email;
        }
        // Create analytics document request
        await this.analyticsDocumentService.shareAnalyticsDocuments({
            requestId,
            clinicId: dto.clinicId,
            brandId: dto.brandId, // Use the brandId from the request
            userId,
            documentType: dto.documentType,
            recipientType: dto.recipientType,
            recipientEmail,
            recipientPhone: dto.recipientPhone,
            startDate: new Date(dto.startDate),
            endDate: new Date(dto.endDate)
        });
        return {
            requestId,
            status: analytics_document_request_entity_1.AnalyticsDocumentStatus.PENDING,
            message: 'Analytics document request has been queued for processing. You will receive an email when the documents are ready.'
        };
    }
    async getAnalyticsDocumentStatus(requestId) {
        return await this.analyticsDocumentService.getAnalyticsDocumentStatus(requestId);
    }
};
exports.AnalyticsController = AnalyticsController;
__decorate([
    (0, common_1.Get)('revenue-chart-data'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get revenue chart data' }),
    (0, track_method_decorator_1.TrackMethod)('get-revenue-chart-data'),
    __param(0, (0, common_1.Query)(new common_1.ValidationPipe({ transform: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [analytics_dto_1.GetRevenueChartDataDto]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getRevenueChartData", null);
__decorate([
    (0, common_1.Get)('collected-payments-chart-data'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get collected payments chart data' }),
    (0, track_method_decorator_1.TrackMethod)('get-collected-payments-chart-data'),
    __param(0, (0, common_1.Query)(new common_1.ValidationPipe({ transform: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [analytics_dto_1.GetRevenueChartDataDto]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getCollectedPaymentsChartData", null);
__decorate([
    (0, common_1.Get)('appointments-chart-data'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get appointments chart data' }),
    (0, track_method_decorator_1.TrackMethod)('get-appointments-chart-data'),
    __param(0, (0, common_1.Query)(new common_1.ValidationPipe({ transform: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [analytics_dto_1.GetAppointmentsChartDataDto]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getAppointmentsChartData", null);
__decorate([
    (0, common_1.Get)('download-report'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Download analytics report' }),
    (0, track_method_decorator_1.TrackMethod)('download-analytics-report'),
    __param(0, (0, common_1.Query)(new common_1.ValidationPipe({ transform: true }))),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [analytics_dto_1.DownloadAnalyticsReportDto, Object]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "downloadReport", null);
__decorate([
    (0, common_1.Get)('doctor-summary'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get doctor performance summary' }),
    (0, track_method_decorator_1.TrackMethod)('get-doctor-summary'),
    __param(0, (0, common_1.Query)(new common_1.ValidationPipe({ transform: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [analytics_dto_1.GetDoctorSummaryDto]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getDoctorSummary", null);
__decorate([
    (0, common_1.Get)('summary'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get clinic performance summary' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns summary data for clinic'
    }),
    (0, track_method_decorator_1.TrackMethod)('get-clinic-summary'),
    __param(0, (0, common_1.Query)(new common_1.ValidationPipe({ transform: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [analytics_dto_1.GetSummaryDto]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getSummary", null);
__decorate([
    (0, common_1.Post)('share-documents'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({
        summary: 'Share analytics documents via email',
        description: 'Generate and share analytics documents (PDF + Excel) for a specific document type and date range'
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Analytics document request created successfully',
        type: share_analytics_documents_dto_1.ShareAnalyticsDocumentsResponseDto
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Invalid request parameters'
    }),
    (0, track_method_decorator_1.TrackMethod)('share-analytics-documents'),
    __param(0, (0, common_1.Body)(new common_1.ValidationPipe({ transform: true }))),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [share_analytics_documents_dto_1.ShareAnalyticsDocumentsDto, Object]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "shareAnalyticsDocuments", null);
__decorate([
    (0, common_1.Get)('share-documents/:requestId/status'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({
        summary: 'Get analytics document request status',
        description: 'Check the status of an analytics document sharing request'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Analytics document request status',
        type: share_analytics_documents_dto_1.AnalyticsDocumentStatusDto
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Analytics document request not found'
    }),
    (0, track_method_decorator_1.TrackMethod)('get-analytics-document-status'),
    __param(0, (0, common_1.Param)('requestId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getAnalyticsDocumentStatus", null);
exports.AnalyticsController = AnalyticsController = __decorate([
    (0, swagger_1.ApiTags)('Analytics'),
    (0, common_1.Controller)('analytics'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [analytics_service_1.AnalyticsService,
        analytics_document_service_1.AnalyticsDocumentService,
        users_service_1.UsersService])
], AnalyticsController);
//# sourceMappingURL=analytics.controller.js.map