import { ClinicAlerts } from './entities/clinicAlerts.entity';
import { Repository } from 'typeorm';
import { CreateClinicAlertDto } from './dto/create-clinicAlerts.dto';
import { UpdateClinicAlertsDto } from './dto/update-clinicAlerts.dto';
export declare class ClinicAlertsService {
    private clinicAlertsRepository;
    constructor(clinicAlertsRepository: Repository<ClinicAlerts>);
    createClinicAlerts(createClinicAlert: CreateClinicAlertDto, brandId: string): Promise<ClinicAlerts>;
    getClinicAlerts(clinicId: string, search?: string): Promise<ClinicAlerts[]>;
    deleteClinicAlert(id: string): Promise<void>;
    updateClinicAlerts(id: string, updateClinicAlertDto: UpdateClinicAlertsDto): Promise<ClinicAlerts>;
}
