"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddLastRouteToUsers1752667379440 = void 0;
const typeorm_1 = require("typeorm");
class AddLastRouteToUsers1752667379440 {
    async up(queryRunner) {
        await queryRunner.addColumn('users', new typeorm_1.TableColumn({
            name: 'last_route',
            type: 'varchar',
            length: '500',
            isNullable: true,
            comment: 'Stores the last visited route path for session memory functionality'
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('users', 'last_route');
    }
}
exports.AddLastRouteToUsers1752667379440 = AddLastRouteToUsers1752667379440;
//# sourceMappingURL=1752667379440-AddLastRouteToUsers.js.map