import { ChatRoomService } from './chat-room.service';
import { CreateChatRoomDto } from './dto/create-chat-room.dto';
import { ChatRoom } from './chat-room.entity';
import { ChatRoomUser } from './chat-room-users.entity';
import { CreateChatMessageDto } from './dto/create-chat-message.dto';
import { ChatRoomMessage } from './chat-room-messages.entity';
import { UpdateChatUserRoomDto } from './dto/update-chat-user-room.dto';
export declare class ChatRoomController {
    private readonly chatRoomService;
    constructor(chatRoomService: ChatRoomService);
    create(createChatRoomDto: CreateChatRoomDto): Promise<ChatRoom>;
    findAllForUser(userId: string): Promise<ChatRoomUser[]>;
    sendMessage(createChatMessageDto: CreateChatMessageDto): Promise<ChatRoomMessage>;
    getChatRoomDetails(chatRoomId: string): Promise<ChatRoom>;
    updateChatRoom(id: string, updateChatRoomDto: UpdateChatUserRoomDto): Promise<ChatRoom>;
}
