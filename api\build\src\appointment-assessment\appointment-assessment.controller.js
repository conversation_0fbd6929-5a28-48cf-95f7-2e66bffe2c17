"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppointmentAssessmentController = void 0;
const common_1 = require("@nestjs/common");
const appointment_assessment_service_1 = require("./appointment-assessment.service");
const appointment_assessment_entity_1 = require("./entities/appointment-assessment.entity");
const create_appointment_assessment_dto_1 = require("./dto/create-appointment-assessment.dto");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../roles/roles.decorator");
const role_enum_1 = require("../roles/role.enum");
const track_method_decorator_1 = require("../utils/new-relic/decorators/track-method.decorator");
let AppointmentAssessmentController = class AppointmentAssessmentController {
    constructor(appointmentAssessmentService, logger) {
        this.appointmentAssessmentService = appointmentAssessmentService;
        this.logger = logger;
    }
    async getAppointmentAssessments(req, search) {
        return this.appointmentAssessmentService.findAll(search, req.user.clinicId);
    }
    createNewAssessment(createAppointmentAssessmentDto, req) {
        try {
            this.logger.log('Creating new assessment', {
                dto: createAppointmentAssessmentDto
            });
            return this.appointmentAssessmentService.createNewAssessment(createAppointmentAssessmentDto, req.user.clinicId, req.user.brandId);
        }
        catch (error) {
            this.logger.error('Error creating new assessment', {
                error,
                createAppointmentAssessmentDto
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
};
exports.AppointmentAssessmentController = AppointmentAssessmentController;
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns the list of assessments',
        isArray: true,
        type: appointment_assessment_entity_1.AppointmentAssessmentEntity
    }),
    (0, swagger_1.ApiQuery)({ name: 'search', type: 'string', required: false }),
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('getAppointmentAssessments-appointment-assessment'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('search')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], AppointmentAssessmentController.prototype, "getAppointmentAssessments", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Creates a new assessment',
        type: appointment_assessment_entity_1.AppointmentAssessmentEntity
    }),
    (0, common_1.Post)(),
    (0, common_1.UsePipes)(common_1.ValidationPipe),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('createNewAssessment-appointment-assessment'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_appointment_assessment_dto_1.CreateAppointmentAssessmentDto, Object]),
    __metadata("design:returntype", void 0)
], AppointmentAssessmentController.prototype, "createNewAssessment", null);
exports.AppointmentAssessmentController = AppointmentAssessmentController = __decorate([
    (0, swagger_1.ApiTags)('Appointments'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('appointment-assessment'),
    __metadata("design:paramtypes", [appointment_assessment_service_1.AppointmentAssessmentService,
        winston_logger_service_1.WinstonLogger])
], AppointmentAssessmentController);
//# sourceMappingURL=appointment-assessment.controller.js.map