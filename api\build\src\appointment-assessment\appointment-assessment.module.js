"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppointmentAssessmentModule = void 0;
const common_1 = require("@nestjs/common");
const appointment_assessment_service_1 = require("./appointment-assessment.service");
const appointment_assessment_controller_1 = require("./appointment-assessment.controller");
const appointment_assessment_entity_1 = require("./entities/appointment-assessment.entity");
const typeorm_1 = require("@nestjs/typeorm");
const role_module_1 = require("../roles/role.module");
let AppointmentAssessmentModule = class AppointmentAssessmentModule {
};
exports.AppointmentAssessmentModule = AppointmentAssessmentModule;
exports.AppointmentAssessmentModule = AppointmentAssessmentModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([appointment_assessment_entity_1.AppointmentAssessmentEntity]), role_module_1.RoleModule],
        controllers: [appointment_assessment_controller_1.AppointmentAssessmentController],
        providers: [appointment_assessment_service_1.AppointmentAssessmentService]
    })
], AppointmentAssessmentModule);
//# sourceMappingURL=appointment-assessment.module.js.map