"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsDocumentService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const winston_logger_service_1 = require("../../utils/logger/winston-logger.service");
const analytics_document_request_entity_1 = require("../entities/analytics-document-request.entity");
const invoice_entity_1 = require("../../invoice/entities/invoice.entity");
const payment_details_entity_1 = require("../../payment-details/entities/payment-details.entity");
const enum_credit_types_1 = require("../../payment-details/enums/enum-credit-types");
const enum_invoice_types_1 = require("../../invoice/enums/enum-invoice-types");
const patient_entity_1 = require("../../patients/entities/patient.entity");
const owner_brand_entity_1 = require("../../owners/entities/owner-brand.entity");
const s3_service_1 = require("../../utils/aws/s3/s3.service");
const XLSX = require("xlsx");
const generatePdf_1 = require("../../utils/generatePdf");
const merge_pdf_image_into_1 = require("../../utils/merge-pdf-image-into");
let AnalyticsDocumentService = class AnalyticsDocumentService {
    constructor(logger, analyticsDocumentRequestRepository, invoiceRepository, paymentDetailsRepository, patientRepository, ownerBrandRepository, s3Service) {
        this.logger = logger;
        this.analyticsDocumentRequestRepository = analyticsDocumentRequestRepository;
        this.invoiceRepository = invoiceRepository;
        this.paymentDetailsRepository = paymentDetailsRepository;
        this.patientRepository = patientRepository;
        this.ownerBrandRepository = ownerBrandRepository;
        this.s3Service = s3Service;
    }
    /**
     * Share analytics documents (PDF + Excel) via email
     */
    async shareAnalyticsDocuments(request) {
        try {
            // Create analytics document request record
            const analyticsRequest = this.analyticsDocumentRequestRepository.create({
                clinicId: request.clinicId,
                brandId: request.brandId,
                userId: request.userId,
                documentType: request.documentType,
                recipientType: request.recipientType,
                recipientEmail: request.recipientEmail,
                recipientPhone: request.recipientPhone,
                startDate: request.startDate,
                endDate: request.endDate,
                status: analytics_document_request_entity_1.AnalyticsDocumentStatus.PENDING
            });
            // Set the ID manually after creation
            analyticsRequest.id = request.requestId;
            await this.analyticsDocumentRequestRepository.save(analyticsRequest);
            // Process documents immediately (Phase 1 - synchronous processing)
            // In Phase 2, this will be moved to background processing
            await this.processAnalyticsDocuments(request.requestId);
            this.logger.log('Analytics document request created and processed', {
                requestId: request.requestId,
                documentType: request.documentType,
                recipientType: request.recipientType
            });
            return request.requestId;
        }
        catch (error) {
            this.logger.error('Failed to create analytics document request', {
                requestId: request.requestId,
                error: error instanceof Error ? error.message : String(error)
            });
            throw error;
        }
    }
    /**
     * Get analytics document request status
     */
    async getAnalyticsDocumentStatus(requestId) {
        const request = await this.analyticsDocumentRequestRepository.findOne({
            where: { id: requestId }
        });
        if (!request) {
            throw new common_1.NotFoundException(`Analytics document request with ID ${requestId} not found`);
        }
        return {
            id: request.id,
            status: request.status,
            documentType: request.documentType,
            recipientType: request.recipientType,
            recipientEmail: request.recipientEmail,
            createdAt: request.createdAt,
            updatedAt: request.updatedAt,
            expiresAt: request.expiresAt,
            errorMessage: request.errorMessage,
            processedAt: request.processedAt,
            documentCount: request.documentCount,
            totalSize: request.totalSize
        };
    }
    /**
     * Process analytics documents in background (called by SQS handler)
     */
    async processAnalyticsDocuments(requestId) {
        try {
            // Update status to processing
            await this.analyticsDocumentRequestRepository.update({ id: requestId }, {
                status: analytics_document_request_entity_1.AnalyticsDocumentStatus.PROCESSING,
                updatedAt: new Date()
            });
            this.logger.log('Starting analytics document processing', {
                requestId
            });
            // Get the request details
            const request = await this.analyticsDocumentRequestRepository.findOne({
                where: { id: requestId }
            });
            if (!request) {
                throw new Error(`Analytics document request with ID ${requestId} not found`);
            }
            // Process documents by type (placeholder - will be implemented in Phase 2)
            const result = await this.processDocumentsByType(request);
            // Send email with documents (placeholder - will be implemented in Phase 2)
            await this.sendAnalyticsEmail(request, result);
            // Update status to completed
            await this.analyticsDocumentRequestRepository.update({ id: requestId }, {
                status: analytics_document_request_entity_1.AnalyticsDocumentStatus.COMPLETED,
                processedAt: new Date(),
                documentCount: result.documentCount,
                totalSize: result.totalSize,
                pdfFileKey: result.pdfFileKey,
                excelFileKey: result.excelFileKey,
                updatedAt: new Date()
            });
            this.logger.log('Analytics document processing completed', {
                requestId,
                documentCount: result.documentCount,
                totalSize: result.totalSize
            });
        }
        catch (error) {
            this.logger.error('Analytics document processing failed', {
                requestId,
                error: error instanceof Error ? error.message : String(error)
            });
            // Update status to failed
            await this.analyticsDocumentRequestRepository.update({ id: requestId }, {
                status: analytics_document_request_entity_1.AnalyticsDocumentStatus.FAILED,
                errorMessage: error instanceof Error ? error.message : String(error),
                updatedAt: new Date()
            });
            // Don't re-throw to avoid infinite SQS retries
        }
    }
    /**
     * Process documents by type - Generate both PDF and Excel files
     */
    async processDocumentsByType(request) {
        this.logger.log('Processing documents for analytics request', {
            requestId: request.id,
            documentType: request.documentType,
            startDate: request.startDate,
            endDate: request.endDate
        });
        // Ensure dates are proper Date objects
        const startDate = new Date(request.startDate);
        const endDate = new Date(request.endDate);
        // Validate period (max 1 month)
        const periodDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
        if (periodDays > 31) {
            throw new Error('Period cannot exceed 1 month (31 days)');
        }
        // Create request object with proper Date objects
        const requestWithDates = {
            ...request,
            startDate,
            endDate
        };
        // Fetch data based on document type
        let documentData = [];
        let excelData = {};
        switch (request.documentType) {
            case analytics_document_request_entity_1.AnalyticsDocumentType.INVOICE:
                documentData = await this.fetchInvoiceData(requestWithDates);
                excelData.invoices = await this.convertInvoicesToExcelFormat(documentData);
                break;
            case analytics_document_request_entity_1.AnalyticsDocumentType.RECEIPT:
                documentData = await this.fetchReceiptData(requestWithDates);
                excelData.receipts = this.convertReceiptsToExcelFormat(documentData);
                break;
            case analytics_document_request_entity_1.AnalyticsDocumentType.CREDIT_NOTE:
                documentData = await this.fetchCreditNoteData(requestWithDates);
                excelData.creditNotes = this.convertCreditNotesToExcelFormat(documentData);
                break;
            default:
                throw new Error(`Unsupported document type: ${request.documentType}`);
        }
        // Enforce document limit (max 5000)
        if (documentData.length > 5000) {
            throw new Error(`Too many documents found (${documentData.length}). Maximum allowed is 5000.`);
        }
        // Generate Excel buffer
        const excelBuffer = this.generateExcelReport(excelData, request.documentType);
        // Generate PDF buffer with actual document stitching
        const pdfBuffer = await this.generatePdfReport(documentData, request);
        const totalSize = excelBuffer.length + pdfBuffer.length;
        // Upload files to S3
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const pdfFileKey = `analytics-documents/${request.clinicId}/${request.documentType.toLowerCase()}-${timestamp}.pdf`;
        const excelFileKey = `analytics-documents/${request.clinicId}/${request.documentType.toLowerCase()}-${timestamp}.xlsx`;
        let pdfUploadResult;
        let excelUploadResult;
        try {
            // Upload PDF to S3
            pdfUploadResult = await this.s3Service.uploadPdfToS3(pdfBuffer, pdfFileKey);
            // Upload Excel to S3
            excelUploadResult = await this.uploadExcelToS3(excelBuffer, excelFileKey);
            this.logger.log('Files uploaded to S3', {
                requestId: request.id,
                pdfFileKey,
                excelFileKey
            });
        }
        catch (error) {
            this.logger.error('Failed to upload files to S3', {
                requestId: request.id,
                error: error instanceof Error ? error.message : String(error)
            });
            throw error;
        }
        this.logger.log('Document processing completed', {
            requestId: request.id,
            documentCount: documentData.length,
            totalSize,
            excelSize: excelBuffer.length,
            pdfSize: pdfBuffer.length
        });
        return {
            pdfBuffer,
            excelBuffer,
            documentCount: documentData.length,
            totalSize,
            pdfFileKey,
            excelFileKey
        };
    }
    /**
     * Fetch invoice data for the specified period
     */
    async fetchInvoiceData(request) {
        this.logger.log('DEBUG: Starting invoice fetch with parameters', {
            requestId: request.id,
            clinicId: request.clinicId,
            brandId: request.brandId,
            startDate: request.startDate,
            endDate: request.endDate
        });
        // First, let's check ALL invoices for this clinic (no filters)
        const allInvoicesForClinic = await this.invoiceRepository
            .createQueryBuilder('invoice')
            .where('invoice.clinicId = :clinicId', { clinicId: request.clinicId })
            .getMany();
        this.logger.log('DEBUG: All invoices for clinic', {
            requestId: request.id,
            clinicId: request.clinicId,
            totalInvoicesForClinic: allInvoicesForClinic.length
        });
        // Now check invoices for clinic + brand
        const invoicesForBrand = await this.invoiceRepository
            .createQueryBuilder('invoice')
            .where('invoice.clinicId = :clinicId', { clinicId: request.clinicId })
            .andWhere('invoice.brandId = :brandId', { brandId: request.brandId })
            .getMany();
        this.logger.log('DEBUG: Invoices for clinic + brand', {
            requestId: request.id,
            clinicId: request.clinicId,
            brandId: request.brandId,
            invoicesForBrand: invoicesForBrand.length
        });
        // Now add date filter
        const invoicesInDateRange = await this.invoiceRepository
            .createQueryBuilder('invoice')
            .where('invoice.clinicId = :clinicId', { clinicId: request.clinicId })
            .andWhere('invoice.brandId = :brandId', { brandId: request.brandId })
            .andWhere('invoice.createdAt >= :startDate', { startDate: request.startDate })
            .andWhere('invoice.createdAt <= :endDate', { endDate: request.endDate })
            .getMany();
        this.logger.log('DEBUG: Invoices in date range', {
            requestId: request.id,
            startDate: request.startDate,
            endDate: request.endDate,
            invoicesInDateRange: invoicesInDateRange.length
        });
        // Finally, add invoice type filter
        const invoices = await this.invoiceRepository
            .createQueryBuilder('invoice')
            .where('invoice.clinicId = :clinicId', { clinicId: request.clinicId })
            .andWhere('invoice.brandId = :brandId', { brandId: request.brandId })
            .andWhere('invoice.createdAt >= :startDate', { startDate: request.startDate })
            .andWhere('invoice.createdAt <= :endDate', { endDate: request.endDate })
            .andWhere('invoice.invoiceType = :invoiceType', { invoiceType: enum_invoice_types_1.EnumInvoiceType.Invoice })
            .orderBy('invoice.createdAt', 'DESC')
            .getMany();
        this.logger.log('Fetched invoice data', {
            requestId: request.id,
            count: invoices.length
        });
        return invoices;
    }
    /**
     * Fetch receipt data for the specified period
     */
    async fetchReceiptData(request) {
        const receipts = await this.paymentDetailsRepository
            .createQueryBuilder('payment')
            .leftJoinAndSelect('payment.patient', 'patient')
            .leftJoinAndSelect('payment.ownerBrand', 'ownerBrand')
            .where('payment.clinicId = :clinicId', { clinicId: request.clinicId })
            .andWhere('payment.brandId = :brandId', { brandId: request.brandId })
            .andWhere('payment.createdAt >= :startDate', { startDate: request.startDate })
            .andWhere('payment.createdAt <= :endDate', { endDate: request.endDate })
            .andWhere('payment.type IN (:...types)', {
            types: [enum_credit_types_1.EnumAmountType.Collect, enum_credit_types_1.EnumAmountType.Return]
        })
            .orderBy('payment.createdAt', 'DESC')
            .getMany();
        this.logger.log('Fetched receipt data', {
            requestId: request.id,
            count: receipts.length
        });
        return receipts;
    }
    /**
     * Fetch credit note data for the specified period
     */
    async fetchCreditNoteData(request) {
        // First, let's check what credit note data exists
        const allCreditNotes = await this.paymentDetailsRepository
            .createQueryBuilder('payment')
            .where('payment.clinicId = :clinicId', { clinicId: request.clinicId })
            .andWhere('payment.brandId = :brandId', { brandId: request.brandId })
            .andWhere('payment.type = :type', { type: enum_credit_types_1.EnumAmountType.CreditNote })
            .getMany();
        this.logger.log('All credit notes for clinic', {
            requestId: request.id,
            clinicId: request.clinicId,
            brandId: request.brandId,
            totalCreditNotes: allCreditNotes.length,
            dateRange: `${request.startDate} to ${request.endDate}`
        });
        // Now get credit notes for the specific date range
        const creditNotes = await this.paymentDetailsRepository
            .createQueryBuilder('payment')
            .where('payment.clinicId = :clinicId', { clinicId: request.clinicId })
            .andWhere('payment.brandId = :brandId', { brandId: request.brandId })
            .andWhere('payment.createdAt >= :startDate', { startDate: request.startDate })
            .andWhere('payment.createdAt <= :endDate', { endDate: request.endDate })
            .andWhere('payment.type = :type', { type: enum_credit_types_1.EnumAmountType.CreditNote })
            .orderBy('payment.createdAt', 'DESC')
            .getMany();
        this.logger.log('Fetched credit note data for date range', {
            requestId: request.id,
            count: creditNotes.length,
            startDate: request.startDate,
            endDate: request.endDate
        });
        // If we found credit notes, let's get the related data using direct repository queries
        const enrichedCreditNotes = [];
        for (const creditNote of creditNotes) {
            const patient = creditNote.patientId ? await this.patientRepository.findOne({
                where: { id: creditNote.patientId }
            }) : null;
            const owner = await this.ownerBrandRepository.findOne({
                where: { id: creditNote.ownerId }
            });
            enrichedCreditNotes.push({
                ...creditNote,
                patient,
                ownerBrand: owner
            });
        }
        return enrichedCreditNotes;
    }
    /**
     * Convert invoice data to Excel format
     */
    async convertInvoicesToExcelFormat(invoices) {
        const excelRows = [];
        for (const invoice of invoices) {
            // Fetch patient data using patientId
            const patient = await this.patientRepository.findOne({
                where: { id: invoice.patientId }
            });
            // Fetch owner data using ownerId
            const owner = await this.ownerBrandRepository.findOne({
                where: { id: invoice.ownerId }
            });
            const ownerName = owner
                ? `${owner.firstName || ''} ${owner.lastName || ''}`.trim()
                : 'N/A';
            const petName = (patient === null || patient === void 0 ? void 0 : patient.patientName) || 'N/A';
            excelRows.push({
                date: invoice.createdAt.toLocaleDateString('en-GB'),
                client: ownerName,
                pet: petName,
                invoiceNumber: invoice.referenceAlphaId || `#${invoice.referenceId}`,
                invoiceStatus: invoice.status || 'Unknown',
                invoiceAmount: Number(invoice.invoiceAmount) || 0,
                invoiceBalance: Number(invoice.balanceDue) || 0
            });
        }
        return excelRows;
    }
    /**
     * Upload Excel file to S3 with proper Promise handling
     */
    async uploadExcelToS3(excelBuffer, fileKey) {
        return new Promise((resolve, reject) => {
            const params = {
                Bucket: this.s3Service['bucketName'], // Access private property
                Key: fileKey,
                Body: excelBuffer,
                ContentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            };
            this.s3Service['s3Client'].putObject(params, (err, data) => {
                if (err) {
                    this.logger.error('S3 Excel upload error', {
                        fileKey,
                        error: err.message || err
                    });
                    reject(err);
                }
                else {
                    this.logger.log('Excel file uploaded to S3 successfully', {
                        fileKey
                    });
                    resolve(data);
                }
            });
        });
    }
    /**
     * Convert receipt data to Excel format
     */
    convertReceiptsToExcelFormat(receipts) {
        return receipts.map(receipt => {
            const ownerName = receipt.ownerBrand
                ? `${receipt.ownerBrand.firstName || ''} ${receipt.ownerBrand.lastName || ''}`.trim()
                : 'N/A';
            const transactionType = receipt.type === enum_credit_types_1.EnumAmountType.Collect ? 'Collected' : 'Returned';
            return {
                date: receipt.createdAt.toLocaleDateString('en-GB'),
                client: ownerName,
                receiptNumber: receipt.referenceAlphaId || `#${receipt.referenceId}`,
                amount: Number(receipt.amount) || 0,
                transaction: transactionType,
                paymentMode: receipt.paymentType || 'Cash'
            };
        });
    }
    /**
     * Convert credit note data to Excel format
     */
    convertCreditNotesToExcelFormat(creditNotes) {
        return creditNotes.map(creditNote => {
            var _a, _b;
            const ownerName = creditNote.ownerBrand
                ? `${creditNote.ownerBrand.firstName || ''} ${creditNote.ownerBrand.lastName || ''}`.trim()
                : 'N/A';
            const referenceInvoice = ((_a = creditNote.invoice) === null || _a === void 0 ? void 0 : _a.referenceAlphaId) ||
                (((_b = creditNote.invoice) === null || _b === void 0 ? void 0 : _b.referenceId) ? `#${creditNote.invoice.referenceId}` : 'N/A');
            return {
                date: creditNote.createdAt.toLocaleDateString('en-GB'),
                client: ownerName,
                creditNoteNumber: creditNote.referenceAlphaId || `#${creditNote.referenceId}`,
                referenceInvoice,
                amountReturned: Number(creditNote.amount) || 0
            };
        });
    }
    /**
     * Generate Excel report from data
     */
    generateExcelReport(data, documentType) {
        const workbook = XLSX.utils.book_new();
        switch (documentType) {
            case analytics_document_request_entity_1.AnalyticsDocumentType.INVOICE:
                if (data.invoices && data.invoices.length > 0) {
                    const worksheet = XLSX.utils.json_to_sheet(data.invoices);
                    // Set column widths for better formatting
                    worksheet['!cols'] = [
                        { width: 12 }, // Date
                        { width: 20 }, // Client
                        { width: 15 }, // Pet
                        { width: 15 }, // Invoice Number
                        { width: 15 }, // Invoice Status
                        { width: 15 }, // Invoice Amount
                        { width: 15 } // Invoice Balance
                    ];
                    XLSX.utils.book_append_sheet(workbook, worksheet, 'Invoices');
                }
                else {
                    // Create empty sheet with headers
                    const emptyData = [{
                            date: '',
                            client: '',
                            pet: '',
                            invoiceNumber: '',
                            invoiceStatus: '',
                            invoiceAmount: '',
                            invoiceBalance: ''
                        }];
                    const worksheet = XLSX.utils.json_to_sheet(emptyData);
                    XLSX.utils.book_append_sheet(workbook, worksheet, 'Invoices');
                }
                break;
            case analytics_document_request_entity_1.AnalyticsDocumentType.RECEIPT:
                if (data.receipts && data.receipts.length > 0) {
                    const worksheet = XLSX.utils.json_to_sheet(data.receipts);
                    worksheet['!cols'] = [
                        { width: 12 }, // Date
                        { width: 20 }, // Client
                        { width: 15 }, // Receipt Number
                        { width: 15 }, // Amount
                        { width: 15 }, // Transaction
                        { width: 15 } // Payment Mode
                    ];
                    XLSX.utils.book_append_sheet(workbook, worksheet, 'Receipts');
                }
                else {
                    const emptyData = [{
                            date: '',
                            client: '',
                            receiptNumber: '',
                            amount: '',
                            transaction: '',
                            paymentMode: ''
                        }];
                    const worksheet = XLSX.utils.json_to_sheet(emptyData);
                    XLSX.utils.book_append_sheet(workbook, worksheet, 'Receipts');
                }
                break;
            case analytics_document_request_entity_1.AnalyticsDocumentType.CREDIT_NOTE:
                if (data.creditNotes && data.creditNotes.length > 0) {
                    const worksheet = XLSX.utils.json_to_sheet(data.creditNotes);
                    worksheet['!cols'] = [
                        { width: 12 }, // Date
                        { width: 20 }, // Client
                        { width: 18 }, // Credit Note Number
                        { width: 18 }, // Reference Invoice
                        { width: 15 } // Amount Returned
                    ];
                    XLSX.utils.book_append_sheet(workbook, worksheet, 'Credit Notes');
                }
                else {
                    const emptyData = [{
                            date: '',
                            client: '',
                            creditNoteNumber: '',
                            referenceInvoice: '',
                            amountReturned: ''
                        }];
                    const worksheet = XLSX.utils.json_to_sheet(emptyData);
                    XLSX.utils.book_append_sheet(workbook, worksheet, 'Credit Notes');
                }
                break;
        }
        // Convert workbook to buffer
        const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
        this.logger.log('Excel report generated', {
            documentType,
            bufferSize: excelBuffer.length
        });
        return excelBuffer;
    }
    /**
     * Generate PDF report by stitching individual document PDFs
     */
    async generatePdfReport(documentData, request) {
        if (documentData.length === 0) {
            // Return a simple "No documents found" PDF
            const noDataHtml = `
				<!DOCTYPE html>
				<html>
				<head>
					<style>
						body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
						h1 { color: #666; }
					</style>
				</head>
				<body>
					<h1>No ${request.documentType.toLowerCase()}s found</h1>
					<p>No documents were found for the specified date range.</p>
					<p>Period: ${new Date(request.startDate).toDateString()} to ${new Date(request.endDate).toDateString()}</p>
				</body>
				</html>
			`;
            return await (0, generatePdf_1.generatePDF)(noDataHtml);
        }
        const pdfBuffers = [];
        // Generate individual PDFs for each document
        for (const document of documentData) {
            try {
                let pdfBuffer;
                switch (request.documentType) {
                    case analytics_document_request_entity_1.AnalyticsDocumentType.INVOICE:
                        pdfBuffer = await this.generateInvoicePdf(document);
                        break;
                    case analytics_document_request_entity_1.AnalyticsDocumentType.RECEIPT:
                        pdfBuffer = await this.generateReceiptPdf(document);
                        break;
                    case analytics_document_request_entity_1.AnalyticsDocumentType.CREDIT_NOTE:
                        pdfBuffer = await this.generateCreditNotePdf(document);
                        break;
                    default:
                        throw new Error(`Unsupported document type: ${request.documentType}`);
                }
                // Validate the PDF buffer before adding to array
                if (!Buffer.isBuffer(pdfBuffer)) {
                    this.logger.error('Invalid PDF buffer generated', {
                        documentId: document.id,
                        bufferType: typeof pdfBuffer,
                        isBuffer: Buffer.isBuffer(pdfBuffer)
                    });
                    continue; // Skip this document
                }
                this.logger.log('PDF generated successfully for document', {
                    documentId: document.id,
                    bufferSize: pdfBuffer.length
                });
                pdfBuffers.push(pdfBuffer);
            }
            catch (error) {
                this.logger.error('Error generating PDF for document', {
                    documentId: document.id,
                    error: error instanceof Error ? error.message : String(error)
                });
                // Continue with other documents instead of failing completely
            }
        }
        // If no PDFs were generated successfully, return the no-data PDF
        if (pdfBuffers.length === 0) {
            const errorHtml = `
				<!DOCTYPE html>
				<html>
				<head>
					<style>
						body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
						h1 { color: #d32f2f; }
					</style>
				</head>
				<body>
					<h1>Error generating documents</h1>
					<p>Unable to generate PDF documents for the specified criteria.</p>
				</body>
				</html>
			`;
            return await (0, generatePdf_1.generatePDF)(errorHtml);
        }
        // Merge all PDFs into a single document
        try {
            this.logger.log('Attempting to merge PDFs', {
                pdfCount: pdfBuffers.length,
                bufferSizes: pdfBuffers.map(buf => Buffer.isBuffer(buf) ? buf.length : 'INVALID')
            });
            const mergedPdfBuffer = await (0, merge_pdf_image_into_1.mergePDFs)(pdfBuffers);
            this.logger.log('PDF report generated successfully', {
                documentType: request.documentType,
                documentCount: documentData.length,
                successfulPdfs: pdfBuffers.length,
                bufferSize: mergedPdfBuffer.length
            });
            return mergedPdfBuffer;
        }
        catch (mergeError) {
            this.logger.error('PDF merge failed', {
                error: mergeError instanceof Error ? mergeError.message : String(mergeError),
                pdfCount: pdfBuffers.length,
                documentType: request.documentType
            });
            // Return a simple error PDF instead of failing completely
            const errorHtml = `
				<!DOCTYPE html>
				<html>
				<head>
					<style>
						body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
						h1 { color: #d32f2f; }
					</style>
				</head>
				<body>
					<h1>PDF Generation Error</h1>
					<p>Unable to merge PDF documents. Please contact support.</p>
					<p>Error: ${mergeError instanceof Error ? mergeError.message : 'Unknown error'}</p>
				</body>
				</html>
			`;
            return await (0, generatePdf_1.generatePDF)(errorHtml);
        }
    }
    /**
     * Generate PDF for a single invoice
     */
    async generateInvoicePdf(invoice) {
        // For now, create a simple HTML representation
        // In a full implementation, you would fetch related data (patient, clinic, etc.)
        const invoiceHtml = `
			<!DOCTYPE html>
			<html>
			<head>
				<style>
					body { font-family: Arial, sans-serif; padding: 20px; }
					.header { text-align: center; margin-bottom: 30px; }
					.invoice-details { margin-bottom: 20px; }
					.amount { font-weight: bold; color: #2e7d32; }
				</style>
			</head>
			<body>
				<div class="header">
					<h2>INVOICE</h2>
				</div>
				<div class="invoice-details">
					<p><strong>Invoice Number:</strong> ${invoice.referenceAlphaId || 'N/A'}</p>
					<p><strong>Date:</strong> ${new Date(invoice.createdAt).toLocaleDateString()}</p>
					<p><strong>Patient ID:</strong> ${invoice.patientId || 'N/A'}</p>
					<p><strong>Amount:</strong> <span class="amount">$${invoice.invoiceAmount || 0}</span></p>
					<p><strong>Status:</strong> ${invoice.status || 'Unknown'}</p>
					<p><strong>Balance Due:</strong> $${invoice.balanceDue || 0}</p>
				</div>
			</body>
			</html>
		`;
        return await (0, generatePdf_1.generatePDF)(invoiceHtml);
    }
    /**
     * Generate PDF for a single receipt
     */
    async generateReceiptPdf(receipt) {
        const receiptHtml = `
			<!DOCTYPE html>
			<html>
			<head>
				<style>
					body { font-family: Arial, sans-serif; padding: 20px; }
					.header { text-align: center; margin-bottom: 30px; }
					.receipt-details { margin-bottom: 20px; }
					.amount { font-weight: bold; color: #1976d2; }
				</style>
			</head>
			<body>
				<div class="header">
					<h2>RECEIPT</h2>
				</div>
				<div class="receipt-details">
					<p><strong>Receipt Number:</strong> ${receipt.referenceAlphaId || 'N/A'}</p>
					<p><strong>Date:</strong> ${new Date(receipt.createdAt).toLocaleDateString()}</p>
					<p><strong>Amount:</strong> <span class="amount">$${receipt.amount || 0}</span></p>
					<p><strong>Payment Mode:</strong> ${receipt.paymentMode || 'N/A'}</p>
					<p><strong>Type:</strong> ${receipt.type || 'N/A'}</p>
				</div>
			</body>
			</html>
		`;
        return await (0, generatePdf_1.generatePDF)(receiptHtml);
    }
    /**
     * Generate PDF for a single credit note
     */
    async generateCreditNotePdf(creditNote) {
        const creditNoteHtml = `
			<!DOCTYPE html>
			<html>
			<head>
				<style>
					body { font-family: Arial, sans-serif; padding: 20px; }
					.header { text-align: center; margin-bottom: 30px; }
					.credit-details { margin-bottom: 20px; }
					.amount { font-weight: bold; color: #d32f2f; }
				</style>
			</head>
			<body>
				<div class="header">
					<h2>CREDIT NOTE</h2>
				</div>
				<div class="credit-details">
					<p><strong>Credit Note Number:</strong> ${creditNote.referenceAlphaId || 'N/A'}</p>
					<p><strong>Date:</strong> ${new Date(creditNote.createdAt).toLocaleDateString()}</p>
					<p><strong>Amount:</strong> <span class="amount">$${creditNote.amount || 0}</span></p>
					<p><strong>Type:</strong> ${creditNote.type || 'N/A'}</p>
					<p><strong>Reference Invoice:</strong> ${creditNote.invoiceId || 'N/A'}</p>
				</div>
			</body>
			</html>
		`;
        return await (0, generatePdf_1.generatePDF)(creditNoteHtml);
    }
    /**
     * Send analytics email with PDF and Excel attachments
     */
    async sendAnalyticsEmail(request, result) {
        try {
            // Generate S3 pre-signed URLs for file access
            const pdfUrl = result.pdfFileKey ? await this.s3Service.getViewPreSignedUrl(result.pdfFileKey) : null;
            const excelUrl = result.excelFileKey ? await this.s3Service.getViewPreSignedUrl(result.excelFileKey) : null;
            // Create email content
            const emailSubject = `Analytics Report - ${request.documentType} (${new Date(request.startDate).toDateString()} to ${new Date(request.endDate).toDateString()})`;
            const emailBody = `
				<h2>Analytics Document Report</h2>
				<p>Your requested analytics report is ready for download.</p>

				<h3>Report Details:</h3>
				<ul>
					<li><strong>Document Type:</strong> ${request.documentType}</li>
					<li><strong>Period:</strong> ${new Date(request.startDate).toDateString()} to ${new Date(request.endDate).toDateString()}</li>
					<li><strong>Documents Found:</strong> ${result.documentCount}</li>
					<li><strong>Total Size:</strong> ${Math.round(result.totalSize / 1024)} KB</li>
				</ul>

				<h3>Download Links:</h3>
				<p>Please use the links below to download your reports. These links will expire in 24 hours.</p>

				${pdfUrl ? `<p><a href="${pdfUrl}" style="color: #1976d2; text-decoration: none;">📄 Download PDF Report</a></p>` : ''}
				${excelUrl ? `<p><a href="${excelUrl}" style="color: #1976d2; text-decoration: none;">📊 Download Excel Report</a></p>` : ''}

				<hr>
				<p style="color: #666; font-size: 12px;">
					This is an automated message from Nidana Analytics System.<br>
					Report generated on ${new Date().toLocaleString()}
				</p>
			`;
            // Log the email sending (in a real implementation, you would use an email service)
            this.logger.log('Analytics email prepared for sending', {
                requestId: request.id,
                recipientEmail: request.recipientEmail,
                documentCount: result.documentCount,
                subject: emailSubject,
                hasPdfAttachment: !!pdfUrl,
                hasExcelAttachment: !!excelUrl
            });
            // TODO: Integrate with actual email service (SES, SendGrid, etc.)
            // Example: await this.emailService.sendEmail({
            //   to: request.recipientEmail,
            //   subject: emailSubject,
            //   html: emailBody
            // });
        }
        catch (error) {
            this.logger.error('Error sending analytics email', {
                requestId: request.id,
                recipientEmail: request.recipientEmail,
                error: error instanceof Error ? error.message : String(error)
            });
            throw error;
        }
    }
};
exports.AnalyticsDocumentService = AnalyticsDocumentService;
exports.AnalyticsDocumentService = AnalyticsDocumentService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, typeorm_1.InjectRepository)(analytics_document_request_entity_1.AnalyticsDocumentRequestEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(invoice_entity_1.InvoiceEntity)),
    __param(3, (0, typeorm_1.InjectRepository)(payment_details_entity_1.PaymentDetailsEntity)),
    __param(4, (0, typeorm_1.InjectRepository)(patient_entity_1.Patient)),
    __param(5, (0, typeorm_1.InjectRepository)(owner_brand_entity_1.OwnerBrand)),
    __metadata("design:paramtypes", [winston_logger_service_1.WinstonLogger,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        s3_service_1.S3Service])
], AnalyticsDocumentService);
//# sourceMappingURL=analytics-document.service.js.map