import * as Http from './http.service';
import {
    DOWNLOAD_ANALYTICS_REPORT,
    GET_REVENUE_CHART_DATA,
    GET_COLLECTED_PAYMENTS_CHART_DATA,
    GET_APPOINTMENTS_CHART_DATA,
    GET_DOCTOR_SUMMARY,
    GET_SUMMARY,
    SHARE_ANALYTICS_DOCUMENTS,
} from './url.service';

export interface DownloadAnalyticsReportDto {
    type: string;
    startDate: string;
    endDate: string;
    clinicId: string;
    reportType?: string;
}

export interface RevenueChartDataDto {
    startDate: string;
    endDate: string;
    clinicId: string;
}

export interface AppointmentsChartDataDto extends RevenueChartDataDto {
    type: 'All' | 'BusiestDays' | 'BusiestHours' | 'AverageDuration';
}

export interface RevenueChartDataPoint {
    date: string;
    products?: number;
    services?: number;
    diagnostics?: number;
    medications?: number;
    vaccinations?: number;
    [key: string]: string | number | undefined;
}

export interface CollectedPaymentsChartDataPoint {
    date: string;
    cash?: number;
    card?: number;
    wallet?: number;
    cheque?: number;
    bankTransfer?: number;
    [key: string]: string | number | undefined;
}

export interface AppointmentsChartDataPoint {
    date: string;
    total?: number;
    missed?: number;
    averageDuration?: number;
    [key: string]: string | number | undefined;
}

export interface AppointmentsChartResponse {
    total: AppointmentsChartDataPoint[];
    missed: AppointmentsChartDataPoint[];
    busiestDays?: {
        day: string;
        count: number;
        weeksCount: number;
        total: number;
    }[];
    busiestHours?: { hour: string; count: number }[];
    averageDuration?: { date: string; duration: number }[];
}

export interface ApiResponse<T> {
    status: boolean;
    data: T;
    message?: string;
}

export interface DoctorSummaryDto {
    startDate: string;
    endDate: string;
    clinicId: string;
}

export interface DoctorSummaryResponse {
    doctorName: string;
    numAppointments: number;
    totalRevenue: number;
    revenuePerAppointment: number;
    avgAppointmentDurationMinutes: number;
}

export interface SummaryDto {
    startDate: string;
    endDate: string;
    clinicId: string;
}

export interface SummaryResponse {
    appointmentsCompleted: number;
    invoicesGenerated: number;
    totalBilling: number;
    amountCollected: {
        cash: number;
        card: number;
        wallet: number;
        cheque: number;
        bankTransfer: number;
        total: number;
    };
}

export const downloadReport = async (dto: DownloadAnalyticsReportDto) => {
    const response = await Http.getWithAuthBlob(DOWNLOAD_ANALYTICS_REPORT(dto));
    return response;
};

export const getRevenueChartData = async (
    dto: RevenueChartDataDto
): Promise<ApiResponse<RevenueChartDataPoint[]>> => {
    const response = await Http.getWithAuth<
        ApiResponse<RevenueChartDataPoint[]>
    >(GET_REVENUE_CHART_DATA(dto));
    return response;
};

export const getCollectedPaymentsChartData = async (
    dto: RevenueChartDataDto
): Promise<ApiResponse<CollectedPaymentsChartDataPoint[]>> => {
    const response = await Http.getWithAuth<
        ApiResponse<CollectedPaymentsChartDataPoint[]>
    >(GET_COLLECTED_PAYMENTS_CHART_DATA(dto));
    return response;
};

export const getAppointmentsChartData = async (
    dto: AppointmentsChartDataDto
): Promise<ApiResponse<AppointmentsChartResponse>> => {
    const response = await Http.getWithAuth<
        ApiResponse<AppointmentsChartResponse>
    >(GET_APPOINTMENTS_CHART_DATA(dto));
    return response;
};

export const getDoctorSummary = async (
    dto: DoctorSummaryDto
): Promise<ApiResponse<DoctorSummaryResponse[]>> => {
    const response = await Http.getWithAuth<
        ApiResponse<DoctorSummaryResponse[]>
    >(GET_DOCTOR_SUMMARY(dto));
    return response;
};

export const getSummary = async (
    dto: SummaryDto
): Promise<SummaryResponse> => {
    const response = await Http.getWithAuth(GET_SUMMARY(dto));
    return response.data;
};

export interface ShareAnalyticsDocumentsDto {
    documentType: 'INVOICE' | 'RECEIPT' | 'CREDIT_NOTE';
    startDate: string;
    endDate: string;
    clinicId: string;
    email: string;
}

export interface ShareAnalyticsDocumentsResponse {
    success: boolean;
    message: string;
    requestId?: string;
}

export const shareAnalyticsDocuments = async (
    dto: ShareAnalyticsDocumentsDto
): Promise<ApiResponse<ShareAnalyticsDocumentsResponse>> => {
    const response = await Http.postWithAuth(SHARE_ANALYTICS_DOCUMENTS(), dto);
    return response;
};