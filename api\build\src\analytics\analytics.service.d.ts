import { Repository } from 'typeorm';
import { InvoiceEntity } from '../invoice/entities/invoice.entity';
import { PaymentDetailsEntity } from '../payment-details/entities/payment-details.entity';
import { OwnerBrand } from '../owners/entities/owner-brand.entity';
import { ClinicEntity } from '../clinics/entities/clinic.entity';
import { DownloadAnalyticsReportDto, GetRevenueChartDataDto, RevenueChartDataPoint, CollectedPaymentsChartDataPoint, GetAppointmentsChartDataDto, AppointmentsChartResponse, GetDoctorSummaryDto, DoctorSummaryResponseDto, GetSummaryDto, SummaryResponseDto } from './dto/analytics.dto';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
export declare class AnalyticsService {
    private readonly invoiceRepository;
    private readonly paymentDetailsRepository;
    private readonly ownerBrandRepository;
    private readonly appointmentRepository;
    private readonly clinicRepository;
    private readonly logger;
    constructor(invoiceRepository: Repository<InvoiceEntity>, paymentDetailsRepository: Repository<PaymentDetailsEntity>, ownerBrandRepository: Repository<OwnerBrand>, appointmentRepository: Repository<AppointmentEntity>, clinicRepository: Repository<ClinicEntity>);
    /**
     * Get the timezone for a specific clinic
     */
    private getClinicTimezone;
    /**
     * Helper function to determine date grouping strategy based on date range
     */
    private _getDateGroupingStrategy;
    private getClinicInfo;
    private createMetadataSheet;
    private cleanDataForExcel;
    private determineColumnProperties;
    private styleWorksheet;
    private _createCellStyles;
    private _applyHeaderStyles;
    private _applyDataCellStyles;
    private _applyCellStyle;
    private _applyNumericCellStyle;
    private _convertToNumericIfPossible;
    private createDataSheet;
    generateReport(dto: DownloadAnalyticsReportDto): Promise<Buffer>;
    private getProductsBillingData;
    private getServicesBillingData;
    private getVaccinationsBillingData;
    private getMedicationsBillingData;
    private getLabReportsBillingData;
    private getPatientsBillingData;
    private getBadDebtData;
    private mapBadDebtRow;
    private getCollectedPaymentsLedgerData;
    private getReturnedPaymentsLedgerData;
    private _buildSectionData;
    private _applyCellStyling;
    private _applyHeaderStyling;
    private _applyNumberFormatting;
    private _applyTotalRowStyling;
    private _isTotalRow;
    private _makeTotalRowBold;
    private _ensureCellStyle;
    private createPaymentsSummarySheet;
    private getPaymentSummaryStats;
    private getOwnerSummaryData;
    getRevenueChartData(dto: GetRevenueChartDataDto): Promise<RevenueChartDataPoint[]>;
    getCollectedPaymentsChartData(dto: GetRevenueChartDataDto): Promise<CollectedPaymentsChartDataPoint[]>;
    getAppointmentsChartData(dto: GetAppointmentsChartDataDto): Promise<AppointmentsChartResponse>;
    getDoctorSummary(dto: GetDoctorSummaryDto): Promise<DoctorSummaryResponseDto[]>;
    getSummary(dto: GetSummaryDto): Promise<SummaryResponseDto>;
    private _addDataSheet;
    private _generateRevenueByBillingReport;
    private _generateRevenueByPatientReport;
    private _generateCollectedPaymentsReport;
    private _createCollectedPaymentsLedgerSheet;
    private _createReturnedPaymentsLedgerSheet;
    /**
     * Optimized method that fetches payment data for all modes in a single query
     * Replaces the need to call getPaymentsByMode multiple times
     */
    private getAllPaymentsByMode;
    private createPaymentModeSheet;
}
