import { Repository } from 'typeorm';
import { ChatRoom } from './chat-room.entity';
import { CreateChatRoomDto } from './dto/create-chat-room.dto';
import { ChatRoomUser } from './chat-room-users.entity';
import { CreateChatMessageDto } from './dto/create-chat-message.dto';
import { ChatRoomMessage } from './chat-room-messages.entity';
import { UpdateChatUserRoomDto } from './dto/update-chat-user-room.dto';
export declare class ChatRoomService {
    private chatRoomRepository;
    private chatRoomUserRepository;
    private chatMessageRepository;
    constructor(chatRoomRepository: Repository<ChatRoom>, chatRoomUserRepository: Repository<ChatRoomUser>, chatMessageRepository: Repository<ChatRoomMessage>);
    create(createChatRoomDto: CreateChatRoomDto): Promise<ChatRoom>;
    findAllForUser(userId: string): Promise<ChatRoomUser[]>;
    sendMessage(createChatMessageDto: CreateChatMessageDto): Promise<ChatRoomMessage>;
    getChatRoomDetails(chatRoomId: string): Promise<ChatRoom>;
    updateChatRoom(id: string, updateChatRoomDto: UpdateChatUserRoomDto): Promise<ChatRoom>;
}
