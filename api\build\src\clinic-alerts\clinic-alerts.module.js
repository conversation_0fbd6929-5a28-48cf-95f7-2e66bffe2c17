"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClinicAlertsModule = void 0;
const common_1 = require("@nestjs/common");
const clinic_alerts_controller_1 = require("./clinic-alerts.controller");
const clinic_alerts_service_1 = require("./clinic-alerts.service");
const typeorm_1 = require("@nestjs/typeorm");
const clinicAlerts_entity_1 = require("./entities/clinicAlerts.entity");
const role_module_1 = require("../roles/role.module");
let ClinicAlertsModule = class ClinicAlertsModule {
};
exports.ClinicAlertsModule = ClinicAlertsModule;
exports.ClinicAlertsModule = ClinicAlertsModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([clinicAlerts_entity_1.ClinicAlerts]), role_module_1.RoleModule],
        controllers: [clinic_alerts_controller_1.ClinicAlertsController],
        providers: [clinic_alerts_service_1.ClinicAlertsService]
    })
], ClinicAlertsModule);
//# sourceMappingURL=clinic-alerts.module.js.map