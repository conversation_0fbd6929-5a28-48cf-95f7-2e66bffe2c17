export declare enum AnalyticsDocumentStatus {
    PENDING = "PENDING",
    PROCESSING = "PROCESSING",
    COMPLETED = "COMPLETED",
    FAILED = "FAILED",
    EXPIRED = "EXPIRED"
}
export declare enum AnalyticsDocumentType {
    INVOICE = "INVOICE",
    RECEIPT = "RECEIPT",
    CREDIT_NOTE = "CREDIT_NOTE"
}
export declare enum AnalyticsRecipientType {
    CLIENT = "CLIENT",
    OTHER = "OTHER"
}
export declare class AnalyticsDocumentRequestEntity {
    id: string;
    clinicId: string;
    brandId: string;
    userId: string;
    documentType: AnalyticsDocumentType;
    recipientType: AnalyticsRecipientType;
    recipientEmail?: string;
    recipientPhone?: string;
    startDate: Date;
    endDate: Date;
    status: AnalyticsDocumentStatus;
    pdfFileKey?: string;
    excelFileKey?: string;
    errorMessage?: string;
    documentCount: number;
    totalSize: number;
    processedAt?: Date;
    processingMetadata?: {
        totalDocuments?: number;
        processedDocuments?: number;
        batchSize?: number;
        startedAt?: Date;
        completedAt?: Date;
    };
    expiresAt: Date;
    createdAt: Date;
    updatedAt: Date;
}
