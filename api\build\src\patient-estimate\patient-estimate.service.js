"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PatientEstimateService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const patient_estimate_entity_1 = require("./entities/patient-estimate.entity");
const uuidv7_1 = require("uuidv7");
const generatePdf_1 = require("../utils/generatePdf");
const send_mail_service_1 = require("../utils/aws/ses/send-mail-service");
const whatsapp_service_1 = require("../utils/whatsapp-integration/whatsapp.service");
const s3_service_1 = require("../utils/aws/s3/s3.service");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const treatmentEstimate_1 = require("../utils/pdfs/treatmentEstimate");
const moment = require("moment");
const generate_alpha_numeric_code_1 = require("../utils/common/generate_alpha-numeric_code");
const mail_template_generator_1 = require("../utils/mail-generator/mail-template-generator");
const constants_1 = require("../utils/constants");
const get_login_url_1 = require("../utils/common/get-login-url");
const whatsapp_template_generator_1 = require("../utils/communicatoins/whatsapp-template-generator");
const template_helper_util_1 = require("../utils/common/template-helper.util");
let PatientEstimateService = class PatientEstimateService {
    constructor(patientEstimateRepository, logger, mailService, whatsappService, s3Service) {
        this.patientEstimateRepository = patientEstimateRepository;
        this.logger = logger;
        this.mailService = mailService;
        this.whatsappService = whatsappService;
        this.s3Service = s3Service;
    }
    async sendMail(body, buffers, fileName, email, subject) {
        try {
            if ((0, get_login_url_1.isProductionOrUat)() && email) {
                this.mailService.sendMail({
                    body: body,
                    subject: subject !== null && subject !== void 0 ? subject : 'signable document attachments',
                    pdfBuffers: buffers,
                    pdfFileNames: fileName,
                    toMailAddress: email
                });
                this.logger.log('Production Mail Log', {
                    body: body,
                    subject: subject !== null && subject !== void 0 ? subject : 'signable document attachments',
                    pdfFileNames: fileName,
                    toMailAddress: constants_1.DEV_SES_EMAIL
                });
            }
            else if (!(0, get_login_url_1.isProduction)()) {
                this.mailService.sendMail({
                    body: body,
                    subject: subject !== null && subject !== void 0 ? subject : 'signable document attachments',
                    pdfBuffers: buffers,
                    pdfFileNames: fileName,
                    toMailAddress: constants_1.DEV_SES_EMAIL
                });
                this.logger.log('UAT Mail Log', {
                    body: body,
                    subject: subject !== null && subject !== void 0 ? subject : 'signable document attachments',
                    pdfFileNames: fileName,
                    toMailAddress: constants_1.DEV_SES_EMAIL
                });
            }
        }
        catch (error) {
            console.log('eroor => ', error);
            this.logger.log('Send Mail Error ', {
                error
            });
        }
    }
    async create(createDto) {
        const response = await this.patientEstimateRepository.create({
            ...createDto
        });
        const patientEstimate = await this.patientEstimateRepository.save(response);
        if (patientEstimate.signatureRequired) {
            await this.sendSignableDocumentUrl(patientEstimate === null || patientEstimate === void 0 ? void 0 : patientEstimate.id, `https://${createDto === null || createDto === void 0 ? void 0 : createDto.urlPath}/estimate-signed-doc/${patientEstimate === null || patientEstimate === void 0 ? void 0 : patientEstimate.id}`);
        }
        else {
            await this.sendEstimateDocument(patientEstimate === null || patientEstimate === void 0 ? void 0 : patientEstimate.id);
        }
        const patientEstimateResponse = await this.patientEstimateRepository.findOne({
            where: { id: response.id },
            relations: ['doctor']
        });
        return patientEstimateResponse;
    }
    async findAll() {
        return await this.patientEstimateRepository.find();
    }
    async findOne(id) {
        const estimate = await this.patientEstimateRepository.findOne({
            where: { id },
            relations: ['clinic', 'clinic.brand']
        });
        if (!estimate) {
            throw new common_1.NotFoundException(`Patient estimate with ID ${id} not found`);
        }
        return estimate;
    }
    async findByPatient(patientId, page = 1, limit = 10, search // Optional search parameter
    ) {
        try {
            this.logger.log('Fetching PatientEstimates', {
                patientId,
                page,
                limit,
                search
            });
            const queryBuilder = this.patientEstimateRepository
                .createQueryBuilder('patientEstimate')
                .leftJoinAndSelect('patientEstimate.doctor', 'user')
                .where('patientEstimate.patientId = :patientId', { patientId });
            // Add search functionality if search term is provided
            const updatedSearch = search === null || search === void 0 ? void 0 : search.trim();
            if (updatedSearch &&
                !'treatment estimate'.includes(updatedSearch.toLowerCase())) {
                this.logger.log('Adding search filter', { updatedSearch });
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('user.firstName ILIKE :updatedSearch', {
                        updatedSearch: `%${updatedSearch}%`
                    }).orWhere('user.lastName ILIKE :updatedSearch', {
                        updatedSearch: `%${updatedSearch}%`
                    });
                }));
            }
            // Apply pagination
            queryBuilder
                .skip((page - 1) * limit)
                // .take(limit)
                .orderBy('patientEstimate.createdAt', 'DESC');
            // Fetch data and total count
            const [data, total] = await queryBuilder.getManyAndCount();
            this.logger.log('PatientEstimates fetched successfully', {
                count: data.length,
                page,
                limit
            });
            return {
                data,
                total,
                page,
                pageCount: Math.ceil(total / limit)
            };
        }
        catch (error) {
            this.logger.error('Error fetching PatientEstimates', {
                error
            });
            throw new common_1.InternalServerErrorException('Failed to fetch PatientEstimates');
        }
    }
    async update(id, updateDto) {
        const estimate = await this.findOne(id);
        Object.assign(estimate, updateDto);
        return await this.patientEstimateRepository.save(estimate);
    }
    async remove(id) {
        const result = await this.patientEstimateRepository.delete(id);
        if (result.affected === 0) {
            throw new common_1.NotFoundException(`Patient estimate with ID ${id} not found`);
        }
    }
    async sendSignableDocumentUrl(estimateId, url) {
        var _a, _b;
        const estimateResponse = await this.patientEstimateRepository.findOne({
            where: { id: estimateId },
            relations: [
                'patient',
                'patient.patientOwners',
                'patient.patientOwners.ownerBrand',
                'patient.patientOwners.ownerBrand.globalOwner',
                'clinic',
                'clinic.brand'
            ]
        });
        if (estimateResponse) {
            (_b = (_a = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.patient) === null || _a === void 0 ? void 0 : _a.patientOwners) === null || _b === void 0 ? void 0 : _b.map(async (patient) => {
                var _a, _b, _c, _d, _e, _f, _g, _h;
                const clientName = `${(_a = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _a === void 0 ? void 0 : _a.firstName} ${(_b = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _b === void 0 ? void 0 : _b.lastName}`;
                const brandName = ((_d = (_c = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.clinic) === null || _c === void 0 ? void 0 : _c.brand) === null || _d === void 0 ? void 0 : _d.name) || '';
                const clientEmail = ((_e = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _e === void 0 ? void 0 : _e.email) || '';
                const clientMobileNumber = ((_g = (_f = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _f === void 0 ? void 0 : _f.globalOwner) === null || _g === void 0 ? void 0 : _g.phoneNumber) || '';
                const patientName = ((_h = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.patient) === null || _h === void 0 ? void 0 : _h.patientName) || '';
                // Extract brand from the original URL for WhatsApp proxy URL
                const urlMatch = url.match(/https:\/\/([^\/]+)\//);
                const fullDomain = urlMatch ? urlMatch[1] : '';
                // Extract just the brand part (subdomain) from domain like "topdogpetsclinic.nidana.io"
                const brand = fullDomain.split('.')[0];
                this.logger.log('URL extraction for WhatsApp', {
                    originalUrl: url,
                    fullDomain,
                    extractedBrand: brand
                });
                // Create WhatsApp proxy URL using centralized format
                const whatsappUrl = `${brand}&patientestimateid=${estimateId}`;
                if (clientEmail === null || clientEmail === void 0 ? void 0 : clientEmail.length) {
                    const { body, subject, email } = (0, mail_template_generator_1.signableDocumentMailTemplate)({
                        clientName,
                        brandName,
                        url,
                        email: clientEmail,
                        patientName
                    });
                    await this.sendMail(body, [], [], email, `Please review and sign ${patientName}’s treatment estimate - ${brandName}`);
                    this.logger.log('email successfully send', estimateId);
                }
                else {
                    this.logger.log('email is not present for user');
                }
                if (clientMobileNumber.length && (0, get_login_url_1.isProductionOrUat)()) {
                    const { templateName, mobileNumber, valuesArray } = (0, whatsapp_template_generator_1.sendTreatmentEstimateUrl)({
                        clientName,
                        brandName,
                        url: whatsappUrl, // Use modified URL for WhatsApp
                        mobileNumber: clientMobileNumber,
                        patientName
                    });
                    const response = await this.whatsappService.sendTemplateMessage({
                        templateName,
                        valuesArray,
                        mobileNumber
                    });
                    this.logger.log('message on whatsapp successfully send', response);
                }
                else {
                    this.logger.log('mobile number is not present for user');
                }
            });
        }
        else {
            this.logger.log('document not found');
        }
    }
    async sendEstimateDocument(estimateId) {
        var _a, _b;
        const estimateResponse = await this.patientEstimateRepository.findOne({
            where: { id: estimateId },
            relations: [
                'patient',
                'patient.patientOwners',
                'patient.patientOwners.ownerBrand',
                'patient.patientOwners.ownerBrand.globalOwner',
                'clinic',
                'clinic.brand'
            ]
        });
        if (estimateResponse) {
            const fileKey = `patient-estimate-library/${estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.patientId}/${(0, uuidv7_1.uuidv4)()}`;
            const documentId = await (0, generate_alpha_numeric_code_1.generateUniqueCode)('documentId', this.patientEstimateRepository);
            const htmlObject = this.generateHtmlObject(estimateResponse, documentId);
            const documentHtml = (0, treatmentEstimate_1.generateTreatmentEstimateHml)(htmlObject);
            const pdfbuffer = await (0, generatePdf_1.generatePDF)(documentHtml);
            await this.s3Service.uploadPdfToS3(pdfbuffer, fileKey);
            const viewSignedUrl = await this.s3Service.getViewPreSignedUrl(fileKey);
            const updateEstimateResponse = await this.patientEstimateRepository.update({ id: estimateId }, {
                signatureStatus: patient_estimate_entity_1.SignatureStatus.PENDING,
                fileKey: fileKey,
                documentId: documentId
            });
            (_b = (_a = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.patient) === null || _a === void 0 ? void 0 : _a.patientOwners) === null || _b === void 0 ? void 0 : _b.map(async (patient) => {
                var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
                const clientName = `${(_a = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _a === void 0 ? void 0 : _a.firstName} ${(_b = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _b === void 0 ? void 0 : _b.lastName}`;
                const brandName = ((_d = (_c = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.clinic) === null || _c === void 0 ? void 0 : _c.brand) === null || _d === void 0 ? void 0 : _d.name) || '';
                const clientEmail = ((_e = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _e === void 0 ? void 0 : _e.email) || '';
                const clientMobileNumber = ((_g = (_f = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _f === void 0 ? void 0 : _f.globalOwner) === null || _g === void 0 ? void 0 : _g.phoneNumber) || '';
                const patientName = ((_h = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.patient) === null || _h === void 0 ? void 0 : _h.patientName) || '';
                const clinicContactNo = ((_k = (_j = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.clinic) === null || _j === void 0 ? void 0 : _j.phoneNumbers[0]) === null || _k === void 0 ? void 0 : _k.number) || '';
                if (clientEmail === null || clientEmail === void 0 ? void 0 : clientEmail.length) {
                    const { body, subject, email } = (0, mail_template_generator_1.nonSignableDocumentMailTemplate)({
                        clientName,
                        patientName,
                        contactNo: clinicContactNo,
                        brandName,
                        email: clientEmail,
                        documentName: 'document for',
                        documentTitle: 'Treatment Estimate'
                    });
                    await this.sendMail(body, [pdfbuffer], [`Treatment_Estimate.pdf`], email, subject);
                    this.logger.log('email successfully send', documentId);
                }
                else {
                    this.logger.log('email is not present for user');
                }
                if (clientMobileNumber.length && (0, get_login_url_1.isProductionOrUat)()) {
                    const templateArgs = {
                        clientName,
                        brandName,
                        contactNo: clinicContactNo,
                        mobileNumber: clientMobileNumber,
                        patientName,
                        documentName: `document for`,
                        documentUrl: viewSignedUrl
                    };
                    const { mobileNumber, templateName, valuesArray } = (0, template_helper_util_1.selectTemplate)(estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.clinic, templateArgs, whatsapp_template_generator_1.sendtreatmentEstimateDocument, whatsapp_template_generator_1.sendtreatmentEstimateDocumentClinicLink);
                    const response = await this.whatsappService.sendTemplateMessage({
                        templateName,
                        valuesArray,
                        mobileNumber
                    });
                    this.logger.log('message on whatsapp successfully send', documentId);
                }
                else {
                    this.logger.log('mobile number is not present for user');
                }
            });
            return fileKey;
        }
        else {
            this.logger.log('document not found');
        }
    }
    async sendSignedDocument(estimateId, updateSignedDocumentDto) {
        var _a, _b;
        this.logger.log('start sending document for id', {
            estimateId,
            updateSignedDocumentDto
        });
        const estimateResponse = await this.patientEstimateRepository.findOne({
            where: { id: estimateId },
            relations: [
                'patient',
                'patient.patientOwners',
                'patient.patientOwners.ownerBrand',
                'patient.patientOwners.ownerBrand.globalOwner',
                'clinic',
                'clinic.brand'
            ]
        });
        if (estimateResponse) {
            try {
                const fileKey = `patient-document-library/${estimateId}/${(0, uuidv7_1.uuidv4)()}`;
                const documentId = await (0, generate_alpha_numeric_code_1.generateUniqueCode)('documentId', this.patientEstimateRepository);
                const htmlObject = this.generateHtmlObject(estimateResponse, documentId);
                const documentHtml = (0, treatmentEstimate_1.generateTreatmentEstimateHml)({
                    ...htmlObject,
                    digitalSignature: updateSignedDocumentDto === null || updateSignedDocumentDto === void 0 ? void 0 : updateSignedDocumentDto.signatureSvg,
                    vetName: `${updateSignedDocumentDto === null || updateSignedDocumentDto === void 0 ? void 0 : updateSignedDocumentDto.firstName} ${updateSignedDocumentDto === null || updateSignedDocumentDto === void 0 ? void 0 : updateSignedDocumentDto.lastName}`,
                    docDate: moment().format('DD MM YYYY')
                });
                const pdfbuffer = await (0, generatePdf_1.generatePDF)(documentHtml);
                await this.s3Service.uploadPdfToS3(pdfbuffer, fileKey);
                const viewSignedUrl = await this.s3Service.getViewPreSignedUrl(fileKey);
                const updateDocumentResponse = await this.patientEstimateRepository.update({ id: estimateId }, {
                    signaturedBy: `${updateSignedDocumentDto.firstName} ${updateSignedDocumentDto.lastName}`,
                    signatureStatus: patient_estimate_entity_1.SignatureStatus.COMPLETED,
                    fileKey,
                    documentId
                });
                (_b = (_a = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.patient) === null || _a === void 0 ? void 0 : _a.patientOwners) === null || _b === void 0 ? void 0 : _b.map(async (patient) => {
                    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
                    const clientName = `${(_a = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _a === void 0 ? void 0 : _a.firstName} ${(_b = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _b === void 0 ? void 0 : _b.lastName}`;
                    const brandName = ((_d = (_c = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.clinic) === null || _c === void 0 ? void 0 : _c.brand) === null || _d === void 0 ? void 0 : _d.name) || '';
                    const clientEmail = ((_e = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _e === void 0 ? void 0 : _e.email) || '';
                    const clientMobileNumber = ((_g = (_f = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _f === void 0 ? void 0 : _f.globalOwner) === null || _g === void 0 ? void 0 : _g.phoneNumber) || '';
                    const patientName = ((_h = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.patient) === null || _h === void 0 ? void 0 : _h.patientName) || '';
                    const clinicContactNo = ((_k = (_j = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.clinic) === null || _j === void 0 ? void 0 : _j.phoneNumbers[0]) === null || _k === void 0 ? void 0 : _k.number) || '';
                    if (clientEmail === null || clientEmail === void 0 ? void 0 : clientEmail.length) {
                        const { body, subject, email } = (0, mail_template_generator_1.nonSignableDocumentMailTemplate)({
                            clientName,
                            patientName,
                            contactNo: clinicContactNo,
                            brandName,
                            email: clientEmail,
                            documentName: 'document for',
                            documentTitle: 'Treatment Estimate'
                        });
                        await this.sendMail(body, [pdfbuffer], [`Treatment_Estimate.pdf`], email, `Your signed copy for Treatment_Estimate is here`);
                        this.logger.log('email successfully send', documentId);
                    }
                    else {
                        this.logger.log('email is not present for user');
                    }
                    if (clientMobileNumber.length && (0, get_login_url_1.isProductionOrUat)()) {
                        const templateArgs = {
                            clientName,
                            brandName,
                            contactNo: clinicContactNo,
                            mobileNumber: clientMobileNumber,
                            patientName,
                            documentName: 'document for',
                            documentUrl: viewSignedUrl
                        };
                        const { mobileNumber, templateName, valuesArray } = (0, template_helper_util_1.selectTemplate)(estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.clinic, templateArgs, whatsapp_template_generator_1.sendtreatmentEstimateDocument, whatsapp_template_generator_1.sendtreatmentEstimateDocumentClinicLink);
                        const response = await this.whatsappService.sendTemplateMessage({
                            templateName,
                            valuesArray,
                            mobileNumber
                        });
                        this.logger.log('message on whatsapp successfully send', documentId);
                    }
                    else {
                        this.logger.log('mobile number is not present for user');
                    }
                });
                this.logger.log('successfully update the data', {
                    id: documentId,
                    documentSendStatus: 'completed',
                    signedBy: {
                        firstName: updateSignedDocumentDto.firstName,
                        lastName: updateSignedDocumentDto === null || updateSignedDocumentDto === void 0 ? void 0 : updateSignedDocumentDto.lastName
                    },
                    signedRecieved: 'completed',
                    fileKey
                });
                return updateDocumentResponse;
            }
            catch (error) {
                this.logger.log('🚀 ~ PatientDocumentLibrariesService ~ error:', error);
            }
        }
        return null;
    }
    generateHtmlObject(estimateResponse, documentId) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7;
        const htmlObject = {
            clinicAddress1: ((_a = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.clinic) === null || _a === void 0 ? void 0 : _a.addressLine1) || '',
            clinicAddress2: `${((_b = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.clinic) === null || _b === void 0 ? void 0 : _b.addressLine2) || ''} ${((_c = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.clinic) === null || _c === void 0 ? void 0 : _c.addressPincode) || ''}` ||
                '',
            clinicAddress3: `${(_d = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.clinic) === null || _d === void 0 ? void 0 : _d.city} ${(_e = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.clinic) === null || _e === void 0 ? void 0 : _e.state} ${(_f = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.clinic) === null || _f === void 0 ? void 0 : _f.country}`,
            clinicContactNo: `${(_h = (_g = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.clinic) === null || _g === void 0 ? void 0 : _g.phoneNumbers[0]) === null || _h === void 0 ? void 0 : _h.country_code}${(_k = (_j = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.clinic) === null || _j === void 0 ? void 0 : _j.phoneNumbers[0]) === null || _k === void 0 ? void 0 : _k.number}`,
            clinicEmail: ((_l = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.clinic) === null || _l === void 0 ? void 0 : _l.email) || '',
            clinicName: (_m = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.clinic) === null || _m === void 0 ? void 0 : _m.name,
            clinicWebsite: (_o = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.clinic) === null || _o === void 0 ? void 0 : _o.website,
            date: moment(estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.createdAt).format('DD MM YYYY'),
            documentId: documentId,
            estimateTotal: estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.estimateTotal,
            ownerEmail: (_r = (_q = (_p = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.patient) === null || _p === void 0 ? void 0 : _p.patientOwners[0]) === null || _q === void 0 ? void 0 : _q.ownerBrand) === null || _r === void 0 ? void 0 : _r.email,
            ownerMobile: `${(_v = (_u = (_t = (_s = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.patient) === null || _s === void 0 ? void 0 : _s.patientOwners[0]) === null || _t === void 0 ? void 0 : _t.ownerBrand) === null || _u === void 0 ? void 0 : _u.globalOwner) === null || _v === void 0 ? void 0 : _v.countryCode}${(_z = (_y = (_x = (_w = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.patient) === null || _w === void 0 ? void 0 : _w.patientOwners[0]) === null || _x === void 0 ? void 0 : _x.ownerBrand) === null || _y === void 0 ? void 0 : _y.globalOwner) === null || _z === void 0 ? void 0 : _z.phoneNumber}`,
            ownerName: `${(_2 = (_1 = (_0 = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.patient) === null || _0 === void 0 ? void 0 : _0.patientOwners[0]) === null || _1 === void 0 ? void 0 : _1.ownerBrand) === null || _2 === void 0 ? void 0 : _2.firstName} ${(_5 = (_4 = (_3 = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.patient) === null || _3 === void 0 ? void 0 : _3.patientOwners[0]) === null || _4 === void 0 ? void 0 : _4.ownerBrand) === null || _5 === void 0 ? void 0 : _5.lastName}`,
            lineItems: estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.treatmentPlan,
            petName: (_6 = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.patient) === null || _6 === void 0 ? void 0 : _6.patientName,
            petBreed: (_7 = estimateResponse === null || estimateResponse === void 0 ? void 0 : estimateResponse.patient) === null || _7 === void 0 ? void 0 : _7.breed
        };
        return htmlObject;
    }
};
exports.PatientEstimateService = PatientEstimateService;
exports.PatientEstimateService = PatientEstimateService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(patient_estimate_entity_1.PatientEstimate)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        winston_logger_service_1.WinstonLogger,
        send_mail_service_1.SESMailService,
        whatsapp_service_1.WhatsappService,
        s3_service_1.S3Service])
], PatientEstimateService);
//# sourceMappingURL=patient-estimate.service.js.map