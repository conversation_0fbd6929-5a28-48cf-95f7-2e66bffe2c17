import React from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { Modal } from '@/app/molecules';
import { Button, Checkbox, Text } from '@/app/atoms';
import RadioButtonGroup, {
    RadioButtonItemsT,
} from '@/app/molecules/RadiobuttonGroup';
import RenderFields from '@/app/molecules/RenderFields';

interface ShareAnalyticsModalProps {
    isOpen: boolean;
    onClose: () => void;
    handleCancel: () => void;
    handleShare: (data: AnalyticsFormValues) => void;
    title: string;
    isLoading?: boolean;
}

// Analytics-specific form values
export type AnalyticsFormValues = {
    documentType: 'INVOICE' | 'RECEIPT' | 'CREDIT_NOTE';
    recipient: 'client' | 'other';
    shareViaEmail: boolean;
    email?: string;
};

// Validation schema for analytics sharing
const AnalyticsSchema = yup.object().shape({
    documentType: yup
        .string()
        .oneOf(['INVOICE', 'RECEIPT', 'CREDIT_NOTE'])
        .required('Document type is required'),
    recipient: yup
        .string()
        .oneOf(['client', 'other'])
        .required('Recipient is required'),
    shareViaEmail: yup.boolean().required(),
    email: yup.string().when('recipient', {
        is: 'other',
        then: (schema) =>
            schema
                .email('Please enter a valid email address')
                .required('Email is required for other recipients'),
        otherwise: (schema) => schema.notRequired(),
    }),
});

const ShareAnalyticsModal: React.FC<ShareAnalyticsModalProps> = ({
    isOpen,
    onClose,
    handleCancel,
    handleShare,
    title,
    isLoading = false,
}) => {
    const documentTypeRadioItems: RadioButtonItemsT[] = [
        {
            id: 'INVOICE',
            label: 'Invoice',
            value: 'INVOICE',
            defaultChecked: true,
        },
        {
            id: 'RECEIPT',
            label: 'Receipt',
            value: 'RECEIPT',
        },
        {
            id: 'CREDIT_NOTE',
            label: 'Credit Note',
            value: 'CREDIT_NOTE',
        },
    ];

    const recipientRadioItems: RadioButtonItemsT[] = [
        {
            id: 'client',
            label: 'Client',
            value: 'client',
            defaultChecked: true,
        },
        { id: 'other', label: 'Other', value: 'other' },
    ];

    const {
        control,
        register,
        formState: { errors, isValid },
        handleSubmit,
        setValue,
        watch,
        trigger,
        reset,
    } = useForm<AnalyticsFormValues>({
        resolver: yupResolver(AnalyticsSchema),
        defaultValues: {
            documentType: 'INVOICE',
            recipient: 'client',
            shareViaEmail: true,
            email: '',
        },
        mode: 'all',
    });

    const onSubmit = (data: AnalyticsFormValues) => {
        handleShare(data);
    };

    const recipient = watch('recipient');
    const shareViaEmail = watch('shareViaEmail');

    // Reset form when modal opens
    React.useEffect(() => {
        if (isOpen) {
            reset({
                documentType: 'INVOICE',
                recipient: 'client',
                shareViaEmail: true,
                email: '',
            });
        }
    }, [isOpen, reset]);

    const renderModalFooter = () => {
        return (
            <div className="flex justify-end gap-x-4">
                <Button
                    id="cancel-share"
                    variant="secondary"
                    type="button"
                    onClick={handleCancel}
                    disabled={isLoading}
                >
                    Cancel
                </Button>
                <Button
                    id="share-analytics"
                    variant="primary"
                    type="button"
                    onClick={handleSubmit(onSubmit)}
                    disabled={!isValid || isLoading}
                >
                    {isLoading ? 'Sharing...' : 'Share Documents'}
                </Button>
            </div>
        );
    };

    return (
        <Modal
            isOpen={isOpen}
            isHeaderBorder={true}
            isModalSubTitle={true}
            modalSubTitle="Generate and share analytics documents via email"
            modalTitle={title}
            onClose={onClose}
            dataAutomation="share-analytics"
            modalFooter={renderModalFooter()}
        >
            <div className="flex flex-col">
                <form
                    id="analytics-share-form"
                    onSubmit={handleSubmit(onSubmit)}
                    className="gap-5 flex flex-col mb-2"
                >
                    {/* Document Type Selection */}
                    <div className="flex flex-col gap-2">
                        <Text variant="bodySmall" fontWeight="font-semibold">
                            Select Document Type
                        </Text>
                        <Controller
                            name="documentType"
                            control={control}
                            render={({ field }) => (
                                <RadioButtonGroup
                                    id="selectDocumentType"
                                    direction="column"
                                    radioButtonItems={documentTypeRadioItems}
                                    isRequired={true}
                                    errorMessage={errors.documentType?.message}
                                    size="medium"
                                    {...field}
                                />
                            )}
                        />
                        {errors.documentType && (
                            <Text variant="caption" textColor="text-red-500">
                                {errors.documentType.message}
                            </Text>
                        )}
                    </div>

                    {/* Recipient Selection */}
                    <div className="flex flex-col gap-2">
                        <Text variant="bodySmall" fontWeight="font-semibold">
                            Select Recipient
                        </Text>
                        <Controller
                            name="recipient"
                            control={control}
                            render={({ field }) => (
                                <RadioButtonGroup
                                    id="selectRecipient"
                                    direction="column"
                                    radioButtonItems={recipientRadioItems}
                                    isRequired={true}
                                    errorMessage={errors.recipient?.message}
                                    size="medium"
                                    {...field}
                                    onChange={(value) => {
                                        field.onChange(value);
                                        trigger('recipient');
                                        // Clear email when switching to client
                                        if (value?.value === 'client') {
                                            setValue('email', '');
                                        }
                                    }}
                                />
                            )}
                        />
                        {errors.recipient && (
                            <Text variant="caption" textColor="text-red-500">
                                {errors.recipient.message}
                            </Text>
                        )}
                    </div>

                    {/* Email Sharing Option */}
                    <div className="flex flex-col gap-2">
                        <Text variant="bodySmall" fontWeight="font-semibold">
                            Sharing Method
                        </Text>
                        <div className="flex items-center gap-2">
                            <Controller
                                name="shareViaEmail"
                                control={control}
                                render={({ field }) => (
                                    <Checkbox
                                        id="shareViaEmail"
                                        checked={field.value}
                                        onChange={(checked) => {
                                            field.onChange(checked);
                                            trigger('shareViaEmail');
                                        }}
                                        label="Share via Email"
                                    />
                                )}
                            />
                        </div>
                    </div>

                    {/* Email Input for Other Recipients */}
                    {recipient === 'other' && shareViaEmail && (
                        <div className="flex flex-col gap-2">
                            <Text
                                variant="bodySmall"
                                fontWeight="font-semibold"
                            >
                                Email Address
                            </Text>
                            <RenderFields
                                control={control}
                                errors={errors}
                                setValue={setValue}
                                watch={watch}
                                fields={[
                                    {
                                        id: 'email-input',
                                        type: 'text-input',
                                        name: 'email',
                                        placeholder: 'Enter email address',
                                        required: true,
                                        className: 'w-full',
                                    },
                                ]}
                            />
                        </div>
                    )}

                    {/* Information Message */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <Text variant="caption" textColor="text-blue-700">
                            📧 You will receive an email with both PDF (stitched
                            documents) and Excel report once processing is
                            complete. This may take a few minutes depending on
                            the number of documents.
                        </Text>
                    </div>
                </form>
            </div>
        </Modal>
    );
};

export default ShareAnalyticsModal;
