import { Repository } from 'typeorm';
import { WinstonLogger } from '../../utils/logger/winston-logger.service';
import { AnalyticsDocumentRequestEntity, AnalyticsDocumentStatus, AnalyticsDocumentType } from '../entities/analytics-document-request.entity';
import { AnalyticsDocumentRequest } from '../interfaces/analytics-sharing.interface';
import { InvoiceEntity } from '../../invoice/entities/invoice.entity';
import { PaymentDetailsEntity } from '../../payment-details/entities/payment-details.entity';
import { Patient } from '../../patients/entities/patient.entity';
import { OwnerBrand } from '../../owners/entities/owner-brand.entity';
import { S3Service } from '../../utils/aws/s3/s3.service';
export declare class AnalyticsDocumentService {
    private readonly logger;
    private readonly analyticsDocumentRequestRepository;
    private readonly invoiceRepository;
    private readonly paymentDetailsRepository;
    private readonly patientRepository;
    private readonly ownerBrandRepository;
    private readonly s3Service;
    constructor(logger: <PERSON><PERSON><PERSON><PERSON>, analyticsDocumentRequestRepository: Repository<AnalyticsDocumentRequestEntity>, invoiceRepository: Repository<InvoiceEntity>, paymentDetailsRepository: Repository<PaymentDetailsEntity>, patientRepository: Repository<Patient>, ownerBrandRepository: Repository<OwnerBrand>, s3Service: S3Service);
    /**
     * Share analytics documents (PDF + Excel) via email
     */
    shareAnalyticsDocuments(request: AnalyticsDocumentRequest): Promise<string>;
    /**
     * Get analytics document request status
     */
    getAnalyticsDocumentStatus(requestId: string): Promise<{
        id: string;
        status: AnalyticsDocumentStatus;
        documentType: AnalyticsDocumentType;
        recipientType: import("../entities/analytics-document-request.entity").AnalyticsRecipientType;
        recipientEmail: string | undefined;
        createdAt: Date;
        updatedAt: Date;
        expiresAt: Date;
        errorMessage: string | undefined;
        processedAt: Date | undefined;
        documentCount: number;
        totalSize: number;
    }>;
    /**
     * Process analytics documents in background (called by SQS handler)
     */
    processAnalyticsDocuments(requestId: string): Promise<void>;
    /**
     * Process documents by type - Generate both PDF and Excel files
     */
    private processDocumentsByType;
    /**
     * Fetch invoice data for the specified period
     */
    private fetchInvoiceData;
    /**
     * Fetch receipt data for the specified period
     */
    private fetchReceiptData;
    /**
     * Fetch credit note data for the specified period
     */
    private fetchCreditNoteData;
    /**
     * Convert invoice data to Excel format
     */
    private convertInvoicesToExcelFormat;
    /**
     * Upload Excel file to S3 with proper Promise handling
     */
    private uploadExcelToS3;
    /**
     * Convert receipt data to Excel format
     */
    private convertReceiptsToExcelFormat;
    /**
     * Convert credit note data to Excel format
     */
    private convertCreditNotesToExcelFormat;
    /**
     * Generate Excel report from data
     */
    private generateExcelReport;
    /**
     * Generate PDF report by stitching individual document PDFs
     */
    private generatePdfReport;
    /**
     * Generate PDF for a single invoice
     */
    private generateInvoicePdf;
    /**
     * Generate PDF for a single receipt
     */
    private generateReceiptPdf;
    /**
     * Generate PDF for a single credit note
     */
    private generateCreditNotePdf;
    /**
     * Send analytics email with PDF and Excel attachments
     */
    private sendAnalyticsEmail;
}
