"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatRoomService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const chat_room_entity_1 = require("./chat-room.entity");
const chat_room_users_entity_1 = require("./chat-room-users.entity");
const uuid = require("uuid");
const chat_room_messages_entity_1 = require("./chat-room-messages.entity");
let ChatRoomService = class ChatRoomService {
    constructor(chatRoomRepository, chatRoomUserRepository, chatMessageRepository) {
        this.chatRoomRepository = chatRoomRepository;
        this.chatRoomUserRepository = chatRoomUserRepository;
        this.chatMessageRepository = chatMessageRepository;
    }
    async create(createChatRoomDto) {
        const chatRoomId = uuid.v4();
        const chatRoom = await this.chatRoomRepository.save({ id: chatRoomId });
        console.log(chatRoom);
        const chatRoomUserPromises = createChatRoomDto.userIds.map(async (id) => {
            const createChatRoomUser = await this.chatRoomUserRepository.save({
                chatRoomId: chatRoom.id,
                userId: id
            });
            if (!createChatRoomUser) {
                throw new common_1.InternalServerErrorException('Failed in creating an chat room user');
            }
        });
        await Promise.all(chatRoomUserPromises);
        return chatRoom;
    }
    async findAllForUser(userId) {
        return this.chatRoomUserRepository.find({
            where: {
                userId: userId
            },
            relations: {
                chatRoom: {
                    users: {
                        clinicUser: {
                            user: true
                        }
                    }
                }
            },
            select: {
                chatRoom: {
                    id: true,
                    lastMessage: true,
                    updatedAt: true,
                    lastMessageSender: true,
                    unreadMessage: true,
                    users: {
                        id: true,
                        clinicUser: {
                            id: true,
                            user: {
                                firstName: true,
                                lastName: true
                            }
                        }
                    }
                }
            }
        });
    }
    async sendMessage(createChatMessageDto) {
        console.log('createChatMessageDto :>> ', createChatMessageDto);
        const { chatRoomId, message, senderId } = createChatMessageDto;
        // Find the chat room to update
        const chatRoom = await this.chatRoomRepository.findOne({
            where: { id: chatRoomId },
            relations: ['messages']
        });
        if (!chatRoom) {
            throw new common_1.InternalServerErrorException('Chat room not found');
        }
        // Update the chat room's last message details
        chatRoom.lastMessage = message;
        chatRoom.lastMessageSender = senderId;
        chatRoom.updatedAt = new Date();
        chatRoom.unreadMessage = chatRoom.unreadMessage + 1;
        await this.chatRoomRepository.save(chatRoom);
        const chatRoomMessage = await this.chatMessageRepository.save({
            ...createChatMessageDto
        });
        return chatRoomMessage;
    }
    async getChatRoomDetails(chatRoomId) {
        const chatRoom = await this.chatRoomRepository.findOne({
            where: {
                id: chatRoomId
            },
            select: {
                users: {
                    id: true,
                    userId: true,
                    clinicUser: {
                        user: {
                            id: true,
                            firstName: true,
                            lastName: true
                        }
                    }
                },
                messages: true
            },
            relations: {
                users: {
                    clinicUser: true
                },
                messages: {
                    user: true
                }
            },
            order: {
                messages: {
                    createdAt: 'ASC'
                }
            }
        });
        if (!chatRoom) {
            throw new common_1.InternalServerErrorException('No chat room found with given id');
        }
        return chatRoom;
    }
    async updateChatRoom(id, updateChatRoomDto) {
        let chatRoom = await this.chatRoomRepository.findOne({
            where: { id }
        });
        console.log('exectution reached here ', { id, updateChatRoomDto });
        if (!chatRoom) {
            throw new common_1.NotFoundException(`chatRoom with ID "${id}" not found`);
        }
        chatRoom = {
            ...chatRoom,
            ...updateChatRoomDto
        };
        return this.chatRoomRepository.save(chatRoom);
    }
};
exports.ChatRoomService = ChatRoomService;
exports.ChatRoomService = ChatRoomService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(chat_room_entity_1.ChatRoom)),
    __param(1, (0, typeorm_1.InjectRepository)(chat_room_users_entity_1.ChatRoomUser)),
    __param(2, (0, typeorm_1.InjectRepository)(chat_room_messages_entity_1.ChatRoomMessage)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], ChatRoomService);
//# sourceMappingURL=chat-room.service.js.map