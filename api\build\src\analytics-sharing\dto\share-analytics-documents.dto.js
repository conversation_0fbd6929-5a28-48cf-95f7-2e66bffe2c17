"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ShareAnalyticsDocumentsResponseDto = exports.AnalyticsDocumentStatusDto = exports.ShareAnalyticsDocumentsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const analytics_document_request_entity_1 = require("../entities/analytics-document-request.entity");
class ShareAnalyticsDocumentsDto {
}
exports.ShareAnalyticsDocumentsDto = ShareAnalyticsDocumentsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type of document to generate',
        enum: analytics_document_request_entity_1.AnalyticsDocumentType,
        example: analytics_document_request_entity_1.AnalyticsDocumentType.INVOICE
    }),
    (0, class_validator_1.IsEnum)(analytics_document_request_entity_1.AnalyticsDocumentType),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ShareAnalyticsDocumentsDto.prototype, "documentType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Start date for document range (YYYY-MM-DD)',
        example: '2024-01-01'
    }),
    (0, class_validator_1.IsDateString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ShareAnalyticsDocumentsDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'End date for document range (YYYY-MM-DD)',
        example: '2024-01-31'
    }),
    (0, class_validator_1.IsDateString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ShareAnalyticsDocumentsDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Clinic ID for filtering documents',
        example: 'uuid-clinic-id'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ShareAnalyticsDocumentsDto.prototype, "clinicId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Brand ID for filtering documents',
        example: 'uuid-brand-id'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ShareAnalyticsDocumentsDto.prototype, "brandId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Recipient type',
        enum: analytics_document_request_entity_1.AnalyticsRecipientType,
        example: analytics_document_request_entity_1.AnalyticsRecipientType.CLIENT
    }),
    (0, class_validator_1.IsEnum)(analytics_document_request_entity_1.AnalyticsRecipientType),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ShareAnalyticsDocumentsDto.prototype, "recipientType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Recipient email address (required for OTHER recipient type)',
        example: '<EMAIL>',
        required: false
    }),
    (0, class_validator_1.ValidateIf)(o => o.recipientType === analytics_document_request_entity_1.AnalyticsRecipientType.OTHER),
    (0, class_validator_1.IsEmail)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ShareAnalyticsDocumentsDto.prototype, "recipientEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Recipient phone number (optional for OTHER recipient type)',
        example: '+1234567890',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ShareAnalyticsDocumentsDto.prototype, "recipientPhone", void 0);
class AnalyticsDocumentStatusDto {
}
exports.AnalyticsDocumentStatusDto = AnalyticsDocumentStatusDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Request ID',
        example: 'uuid-request-id'
    }),
    __metadata("design:type", String)
], AnalyticsDocumentStatusDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current status of the request',
        enum: analytics_document_request_entity_1.AnalyticsDocumentStatus,
        example: analytics_document_request_entity_1.AnalyticsDocumentStatus.COMPLETED
    }),
    __metadata("design:type", String)
], AnalyticsDocumentStatusDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Document type being processed',
        enum: analytics_document_request_entity_1.AnalyticsDocumentType,
        example: analytics_document_request_entity_1.AnalyticsDocumentType.INVOICE
    }),
    __metadata("design:type", String)
], AnalyticsDocumentStatusDto.prototype, "documentType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of documents found and processed',
        example: 25
    }),
    __metadata("design:type", Number)
], AnalyticsDocumentStatusDto.prototype, "documentCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Error message if status is FAILED',
        required: false
    }),
    __metadata("design:type", String)
], AnalyticsDocumentStatusDto.prototype, "errorMessage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Processing metadata',
        required: false
    }),
    __metadata("design:type", Object)
], AnalyticsDocumentStatusDto.prototype, "processingMetadata", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Request creation timestamp'
    }),
    __metadata("design:type", Date)
], AnalyticsDocumentStatusDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Request expiration timestamp'
    }),
    __metadata("design:type", Date)
], AnalyticsDocumentStatusDto.prototype, "expiresAt", void 0);
class ShareAnalyticsDocumentsResponseDto {
}
exports.ShareAnalyticsDocumentsResponseDto = ShareAnalyticsDocumentsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Request ID for tracking',
        example: 'uuid-request-id'
    }),
    __metadata("design:type", String)
], ShareAnalyticsDocumentsResponseDto.prototype, "requestId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Initial status of the request',
        enum: analytics_document_request_entity_1.AnalyticsDocumentStatus,
        example: analytics_document_request_entity_1.AnalyticsDocumentStatus.PENDING
    }),
    __metadata("design:type", String)
], ShareAnalyticsDocumentsResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Message describing the request status'
    }),
    __metadata("design:type", String)
], ShareAnalyticsDocumentsResponseDto.prototype, "message", void 0);
//# sourceMappingURL=share-analytics-documents.dto.js.map