import { AnalyticsDocumentType, AnalyticsRecipientType, AnalyticsDocumentStatus } from '../entities/analytics-document-request.entity';
export declare class ShareAnalyticsDocumentsDto {
    documentType: AnalyticsDocumentType;
    startDate: string;
    endDate: string;
    clinicId: string;
    brandId: string;
    recipientType: AnalyticsRecipientType;
    recipientEmail?: string;
    recipientPhone?: string;
}
export declare class AnalyticsDocumentStatusDto {
    id: string;
    status: AnalyticsDocumentStatus;
    documentType: AnalyticsDocumentType;
    documentCount: number;
    errorMessage?: string;
    processingMetadata?: {
        totalDocuments?: number;
        processedDocuments?: number;
        batchSize?: number;
        startedAt?: Date;
        completedAt?: Date;
    };
    createdAt: Date;
    expiresAt: Date;
}
export declare class ShareAnalyticsDocumentsResponseDto {
    requestId: string;
    status: AnalyticsDocumentStatus;
    message: string;
}
