"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsDocumentRequestEntity = exports.AnalyticsRecipientType = exports.AnalyticsDocumentType = exports.AnalyticsDocumentStatus = void 0;
const typeorm_1 = require("typeorm");
var AnalyticsDocumentStatus;
(function (AnalyticsDocumentStatus) {
    AnalyticsDocumentStatus["PENDING"] = "PENDING";
    AnalyticsDocumentStatus["PROCESSING"] = "PROCESSING";
    AnalyticsDocumentStatus["COMPLETED"] = "COMPLETED";
    AnalyticsDocumentStatus["FAILED"] = "FAILED";
    AnalyticsDocumentStatus["EXPIRED"] = "EXPIRED";
})(AnalyticsDocumentStatus || (exports.AnalyticsDocumentStatus = AnalyticsDocumentStatus = {}));
var AnalyticsDocumentType;
(function (AnalyticsDocumentType) {
    AnalyticsDocumentType["INVOICE"] = "INVOICE";
    AnalyticsDocumentType["RECEIPT"] = "RECEIPT";
    AnalyticsDocumentType["CREDIT_NOTE"] = "CREDIT_NOTE";
})(AnalyticsDocumentType || (exports.AnalyticsDocumentType = AnalyticsDocumentType = {}));
var AnalyticsRecipientType;
(function (AnalyticsRecipientType) {
    AnalyticsRecipientType["CLIENT"] = "CLIENT";
    AnalyticsRecipientType["OTHER"] = "OTHER";
})(AnalyticsRecipientType || (exports.AnalyticsRecipientType = AnalyticsRecipientType = {}));
let AnalyticsDocumentRequestEntity = class AnalyticsDocumentRequestEntity {
};
exports.AnalyticsDocumentRequestEntity = AnalyticsDocumentRequestEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], AnalyticsDocumentRequestEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', name: 'clinic_id' }),
    __metadata("design:type", String)
], AnalyticsDocumentRequestEntity.prototype, "clinicId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', name: 'brand_id' }),
    __metadata("design:type", String)
], AnalyticsDocumentRequestEntity.prototype, "brandId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', name: 'user_id' }),
    __metadata("design:type", String)
], AnalyticsDocumentRequestEntity.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: AnalyticsDocumentType,
        name: 'document_type'
    }),
    __metadata("design:type", String)
], AnalyticsDocumentRequestEntity.prototype, "documentType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: AnalyticsRecipientType,
        name: 'recipient_type'
    }),
    __metadata("design:type", String)
], AnalyticsDocumentRequestEntity.prototype, "recipientType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'recipient_email', nullable: true }),
    __metadata("design:type", String)
], AnalyticsDocumentRequestEntity.prototype, "recipientEmail", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'recipient_phone', nullable: true }),
    __metadata("design:type", String)
], AnalyticsDocumentRequestEntity.prototype, "recipientPhone", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', name: 'start_date' }),
    __metadata("design:type", Date)
], AnalyticsDocumentRequestEntity.prototype, "startDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', name: 'end_date' }),
    __metadata("design:type", Date)
], AnalyticsDocumentRequestEntity.prototype, "endDate", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: AnalyticsDocumentStatus,
        name: 'status',
        default: AnalyticsDocumentStatus.PENDING
    }),
    __metadata("design:type", String)
], AnalyticsDocumentRequestEntity.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'pdf_file_key', nullable: true }),
    __metadata("design:type", String)
], AnalyticsDocumentRequestEntity.prototype, "pdfFileKey", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'excel_file_key', nullable: true }),
    __metadata("design:type", String)
], AnalyticsDocumentRequestEntity.prototype, "excelFileKey", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', name: 'error_message', nullable: true }),
    __metadata("design:type", String)
], AnalyticsDocumentRequestEntity.prototype, "errorMessage", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'integer', name: 'document_count', default: 0 }),
    __metadata("design:type", Number)
], AnalyticsDocumentRequestEntity.prototype, "documentCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'bigint', name: 'total_size', default: 0 }),
    __metadata("design:type", Number)
], AnalyticsDocumentRequestEntity.prototype, "totalSize", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', name: 'processed_at', nullable: true }),
    __metadata("design:type", Date)
], AnalyticsDocumentRequestEntity.prototype, "processedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', name: 'processing_metadata', nullable: true }),
    __metadata("design:type", Object)
], AnalyticsDocumentRequestEntity.prototype, "processingMetadata", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'timestamp',
        name: 'expires_at',
        default: () => "CURRENT_TIMESTAMP + INTERVAL '7 days'"
    }),
    __metadata("design:type", Date)
], AnalyticsDocumentRequestEntity.prototype, "expiresAt", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], AnalyticsDocumentRequestEntity.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], AnalyticsDocumentRequestEntity.prototype, "updatedAt", void 0);
exports.AnalyticsDocumentRequestEntity = AnalyticsDocumentRequestEntity = __decorate([
    (0, typeorm_1.Entity)('analytics_document_requests'),
    (0, typeorm_1.Index)(['clinicId', 'createdAt']),
    (0, typeorm_1.Index)(['status', 'createdAt']),
    (0, typeorm_1.Index)(['expiresAt'])
], AnalyticsDocumentRequestEntity);
//# sourceMappingURL=analytics-document-request.entity.js.map