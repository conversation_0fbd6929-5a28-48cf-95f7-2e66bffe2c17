import { ApiDocumentationBase } from '../base/api-documentation-base';
import { <PERSON><PERSON>og<PERSON> } from '../utils/logger/winston-logger.service';
import { ClinicAlertsService } from './clinic-alerts.service';
import { CreateClinicAlertDto } from './dto/create-clinicAlerts.dto';
import { UpdateClinicAlertsDto } from './dto/update-clinicAlerts.dto';
export declare class ClinicAlertsController extends ApiDocumentationBase {
    private readonly logger;
    private readonly clinicAlertsService;
    constructor(logger: WinstonLogger, clinicAlertsService: ClinicAlertsService);
    createClinicAlerts(createClinicAlertDto: CreateClinicAlertDto, req: {
        user: {
            clinicId: string;
            brandId: string;
        };
    }): Promise<import("./entities/clinicAlerts.entity").ClinicAlerts>;
    getClinicAlerts(clinicId: string, search?: string): Promise<import("./entities/clinicAlerts.entity").ClinicAlerts[]>;
    deleteClinicAlert(id: string): Promise<void>;
    updateClinicAlert(id: string, updateClinicAlertDto: UpdateClinicAlertsDto): Promise<import("./entities/clinicAlerts.entity").ClinicAlerts>;
}
