"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClinicAlertsService = void 0;
const common_1 = require("@nestjs/common");
const clinicAlerts_entity_1 = require("./entities/clinicAlerts.entity");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
let ClinicAlertsService = class ClinicAlertsService {
    constructor(clinicAlertsRepository) {
        this.clinicAlertsRepository = clinicAlertsRepository;
    }
    async createClinicAlerts(createClinicAlert, brandId) {
        return this.clinicAlertsRepository.save({
            ...createClinicAlert,
            brandId: brandId
        });
    }
    async getClinicAlerts(clinicId, search) {
        return this.clinicAlertsRepository.find({
            where: { clinicId, alertName: search }
        });
    }
    async deleteClinicAlert(id) {
        const result = await this.clinicAlertsRepository.delete(id);
        if (result.affected === 0) {
            throw new common_1.NotFoundException(`This clinic-alerts with id: ${id} doesn't exist`);
        }
    }
    async updateClinicAlerts(id, updateClinicAlertDto) {
        const clinicAlert = await this.clinicAlertsRepository.findOne({
            where: { id }
        });
        if (!clinicAlert) {
            throw new common_1.NotFoundException(`clinic-alert with ID "${id}" not found`);
        }
        Object.assign(clinicAlert, updateClinicAlertDto);
        const updatedClinicAlert = await this.clinicAlertsRepository.save(clinicAlert);
        return updatedClinicAlert;
    }
};
exports.ClinicAlertsService = ClinicAlertsService;
exports.ClinicAlertsService = ClinicAlertsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(clinicAlerts_entity_1.ClinicAlerts)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ClinicAlertsService);
//# sourceMappingURL=clinic-alerts.service.js.map