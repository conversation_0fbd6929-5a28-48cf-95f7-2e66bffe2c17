import React, { useState, useMemo } from 'react';
import { Button, Text } from '@/app/atoms';
import HorizontalTabs, { TabItemType } from '@/app/atoms/HorizontalTabs';
import { Share } from 'lucide-react';
import RangeDatePicker from '@/app/molecules/RangeDatePicker';
import { useSummary } from '@/app/services/analytics.queries';
import { getAuth } from '@/app/services/identity.service';
import { Table } from '@/app/organisms/new/table/Table';
import { ColumnDefinition } from '@/app/types/table';
import ShareAnalyticsModal, {
    AnalyticsFormValues,
} from '@/app/organisms/analytics/ShareAnalyticsModal';
import { shareAnalyticsDocuments } from '@/app/services/analytics.service';

interface SummaryProps {
    // Keep props to maintain compatibility, but mark as optional since they're unused
    errors?: { [key: string]: { message: string } };
    control?: any;
    setValue?: any;
    getValues?: any;
    watch?: any;
    onSummaryChange?: (value: string) => void;
    onSummaryBlur?: (value: string) => void;
}

// Define data structure
interface SummaryData {
    appointmentsCompleted: number;
    invoicesGenerated: number;
    totalBilling: number;
    creditNotesGenerated: number;
    totalCreditNotes: number;
    amountCollected: {
        cash: number;
        card: number;
        wallet: number;
        cheque: number;
        bankTransfer: number;
        total: number;
    };
    amountRefunded: {
        cash: number;
        card: number;
        wallet: number;
        cheque: number;
        bankTransfer: number;
        total: number;
    };
}

// Interface for payment methods table
interface PaymentMethod {
    type: string;
    amount: number;
}

// Type for the calculated net amount
interface NetAmount {
    cash: number;
    card: number;
    wallet: number;
    cheque: number;
    bankTransfer: number;
    total: number;
}

const Summary: React.FC<SummaryProps> = ({
    // Keep props for backward compatibility
    errors,
    control,
    setValue,
    getValues,
    watch,
    onSummaryChange,
    onSummaryBlur,
}) => {
    // States for time period selection
    const [timeTab, setTimeTab] = useState('Today');
    const [customDateRange, setCustomDateRange] = useState<{
        startDate: Date | null;
        endDate: Date | null;
    }>({
        startDate: null,
        endDate: null,
    });

    // States for share functionality
    const [isShareModal, setIsShareModal] = useState(false);
    const [isShareLoading, setIsShareLoading] = useState(false);

    // Get current user auth data
    const auth = getAuth();
    const currentUser = auth ? JSON.parse(auth) : null;

    // Format date helper function
    const formatDate = (date: Date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    const getDateRange = useMemo(() => {
        const endDate = new Date();
        let startDate = new Date();

        if (timeTab === 'Custom') {
            if (customDateRange.startDate && customDateRange.endDate) {
                return {
                    startDate: formatDate(customDateRange.startDate),
                    endDate: formatDate(customDateRange.endDate),
                };
            }
            // Return empty strings if custom date range is incomplete
            return { startDate: '', endDate: '' };
        }

        switch (timeTab) {
            case 'Today':
                startDate = new Date();
                break;
            case '1W':
                startDate = new Date();
                startDate.setDate(endDate.getDate() - 6);
                break;
            case '1M':
                startDate = new Date();
                startDate.setMonth(endDate.getMonth() - 1);
                startDate.setDate(startDate.getDate() + 1);
                break;
            case '1Y':
                startDate = new Date();
                startDate.setFullYear(endDate.getFullYear() - 1);
                startDate.setDate(startDate.getDate() + 1);
                break;
            default:
                startDate = new Date();
        }

        // Set time to start of day for startDate and end of day for endDate
        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(23, 59, 59, 999);

        return {
            startDate: formatDate(startDate),
            endDate: formatDate(endDate),
        };
    }, [timeTab, customDateRange]);

    // Fetch summary data based on selected time period
    const { data: summaryData, isLoading } = useSummary({
        startDate: getDateRange.startDate,
        endDate: getDateRange.endDate,
        clinicId: getAuth()?.clinicId,
    });

    const timeTabItems: TabItemType[] = useMemo(
        () => [
            { id: 'Today', label: 'Today' },
            { id: '1W', label: '1 Week' },
            { id: '1M', label: '1 Month' },
            { id: '1Y', label: '1 Year' },
            { id: 'Custom', label: 'Custom' },
        ],
        []
    );

    const handleTimeTabChange = (tab: TabItemType) => {
        setTimeTab(tab.id);
        if (tab.id === 'Custom') {
            const endDate = new Date();
            const startDate = new Date(
                endDate.getFullYear(),
                endDate.getMonth(),
                1
            );
            setCustomDateRange({
                startDate: startDate,
                endDate: endDate,
            });
        }
    };

    const handleCustomDateChange = (dates: {
        startDate: Date | null;
        endDate: Date | null;
    }) => {
        if (dates.startDate && !dates.endDate) {
            setCustomDateRange({
                startDate: dates.startDate,
                endDate: null,
            });
            return;
        }

        if (dates.startDate && dates.endDate) {
            const startDate = new Date(dates.startDate);
            const endDate = new Date(dates.endDate);

            startDate.setHours(0, 0, 0, 0);
            endDate.setHours(23, 59, 59, 999);

            setCustomDateRange({
                startDate: startDate,
                endDate: endDate,
            });
        } else {
            setCustomDateRange(dates);
        }
    };

    // Handle share analytics documents
    const handleShareAnalytics = async (data: AnalyticsFormValues) => {
        try {
            setIsShareLoading(true);

            // Determine the email to use
            let emailToUse = '';
            if (data.recipient === 'client') {
                // For client, we would typically get the email from the patient/owner data
                // For now, we'll use a placeholder or the current user's email
                emailToUse = currentUser?.email || '<EMAIL>';
                console.log('Client email (placeholder):', emailToUse);
            } else if (data.recipient === 'other' && data.email) {
                emailToUse = data.email;
                console.log('Other recipient email:', emailToUse);
            }

            console.log(
                'Share analytics request - Email being sent to:',
                emailToUse
            );

            // Prepare API request data
            const shareRequest = {
                documentType: data.documentType,
                startDate: getDateRange.startDate,
                endDate: getDateRange.endDate,
                clinicId: currentUser?.clinicId || '',
                email: emailToUse,
            };

            console.log('API Request payload:', shareRequest);

            // Call the analytics sharing API
            const response = await shareAnalyticsDocuments(shareRequest);

            if (response.status && response.data?.success) {
                console.log(
                    'Analytics documents sharing initiated successfully:',
                    response.data
                );
                setIsShareModal(false);
                // TODO: Show success toast notification
                alert(
                    'Analytics documents sharing initiated! You will receive an email once processing is complete.'
                );
            } else {
                console.error('Failed to share analytics documents:', response);
                // TODO: Show error toast notification
                alert('Failed to initiate document sharing. Please try again.');
            }

            setIsShareLoading(false);
        } catch (error) {
            console.error('Error sharing analytics documents:', error);
            setIsShareLoading(false);
            // TODO: Show error toast notification
            alert(
                'An error occurred while sharing documents. Please try again.'
            );
        }
    };

    // Format currency values
    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('en-IN', {
            style: 'decimal',
            maximumFractionDigits: 0,
        }).format(value);
    };

    // Calculate net amount
    const calculateNetAmount = (data: SummaryData): NetAmount => {
        return {
            cash: data.amountCollected.cash - data.amountRefunded.cash,
            card: data.amountCollected.card - data.amountRefunded.card,
            wallet: data.amountCollected.wallet - data.amountRefunded.wallet,
            cheque: data.amountCollected.cheque - data.amountRefunded.cheque,
            bankTransfer:
                data.amountCollected.bankTransfer -
                data.amountRefunded.bankTransfer,
            total: data.amountCollected.total - data.amountRefunded.total,
        };
    };

    // Convert summary data to table format
    const tableData = summaryData ? [summaryData] : [];

    // Memoized table cell components
    const BlackHeaderCellTable = React.memo(
        ({ data, bgColor }: { data: any; bgColor?: string }) => {
            // Define columns for payment methods table
            const paymentColumns: ColumnDefinition<PaymentMethod>[] = [
                {
                    key: 'blankCell',
                    header: '',
                    width: '100%',
                    renderCell: () => (
                        <div className="text-center font-[Inter] font-normal text-xs leading-4 align-middle text-neutral-800">
                            {data}
                        </div>
                    ),
                    headCellClassName: '!border-none !border-t-[#E0E0E0]',
                    cellClassName: `!px-0 !py-0 ${bgColor} !border-b-none`,
                },
            ];

            return (
                <div className="w-full">
                    <Table<PaymentMethod>
                        columns={paymentColumns}
                        data={[{} as PaymentMethod]} // We only need one row as we're showing the data in columns
                        variant="borderless"
                        className="w-full"
                    />
                </div>
            );
        }
    );

    // Create component for payment breakdown data
    const PaymentBreakdown = React.memo(
        ({ data, bgColour }: { data: any; bgColour?: string }) => {
            // Define columns for payment methods table
            const paymentColumns: ColumnDefinition<PaymentMethod>[] = [
                {
                    key: 'cash',
                    header: 'Cash',
                    width: '16.6%',
                    renderCell: () => (
                        <div className="text-center font-[Inter] font-normal text-xs leading-4 align-middle text-neutral-800">
                            {formatCurrency(data.cash)}
                        </div>
                    ),
                    headCellClassName: `text-center py-2 px-2 font-medium text-gray-600 !border-t !border-t-[#E0E0E0] font-[Inter] font-semibold text-[10px] leading-4 align-middle ${bgColour}`,
                    cellClassName: 'py-2 px-2 !border-b-none',
                },
                {
                    key: 'card',
                    header: 'Card',
                    width: '16.6%',
                    renderCell: () => (
                        <div className="text-center font-[Inter] font-normal text-xs leading-4 align-middle text-neutral-800">
                            {formatCurrency(data.card)}
                        </div>
                    ),
                    headCellClassName: `text-center py-2 px-2 font-medium text-gray-600 !border-t !border-t-[#E0E0E0] font-[Inter] font-semibold text-[10px] leading-4 align-middle ${bgColour}`,
                    cellClassName: 'py-2 px-2 !border-b-none',
                },
                {
                    key: 'wallet',
                    header: 'Wallet',
                    width: '16.6%',
                    renderCell: () => (
                        <div className="text-center font-[Inter] font-normal text-xs leading-4 align-middle text-neutral-800">
                            {formatCurrency(data.wallet)}
                        </div>
                    ),
                    headCellClassName: `text-center py-2 px-2 font-medium text-gray-600 !border-t !border-t-[#E0E0E0] font-[Inter] font-semibold text-[10px] leading-4 align-middle ${bgColour}`,
                    cellClassName: 'py-2 px-2 !border-b-none',
                },
                {
                    key: 'cheque',
                    header: 'Cheque',
                    width: '16.6%',
                    renderCell: () => (
                        <div className="text-center font-[Inter] font-normal text-xs leading-4 align-middle text-neutral-800">
                            {formatCurrency(data.cheque)}
                        </div>
                    ),
                    headCellClassName: `text-center py-2 px-2 font-medium text-gray-600 !border-t !border-t-[#E0E0E0] font-[Inter] font-semibold text-[10px] leading-4 align-middle ${bgColour}`,
                    cellClassName: 'py-2 px-2 !border-b-none',
                },
                {
                    key: 'bankTransfer',
                    header: 'Bank Transfer',
                    width: '16.6%',
                    renderCell: () => (
                        <div className="text-center font-[Inter] font-normal text-xs leading-4 align-middle text-neutral-800">
                            {formatCurrency(data.bankTransfer)}
                        </div>
                    ),
                    headCellClassName: `text-center  px-2 font-medium text-gray-600 !border-t !border-t-[#E0E0E0] font-[Inter] font-semibold text-[10px] leading-4 align-middle ${bgColour}`,
                    cellClassName: 'py-2 px-2 !border-b-none',
                },
                {
                    key: 'total',
                    header: 'Total',
                    width: '16.6%',
                    renderCell: () => (
                        <div className="text-center font-medium font-[Inter] text-xs leading-4 align-middle text-neutral-800">
                            {formatCurrency(data.total)}
                        </div>
                    ),
                    headCellClassName: `text-center px-2 text-gray-600 !border-t !border-t-[#E0E0E0] font-[Inter] font-extrabold text-[10px] leading-4 align-middle !text-primary-400 ${bgColour}`,
                    cellClassName: 'py-2 px-2 !border-b-none ',
                },
            ];

            return (
                <div className="w-full">
                    <Table<PaymentMethod>
                        columns={paymentColumns}
                        data={[{} as PaymentMethod]} // We only need one row as we're showing the data in columns
                        variant="borderless"
                        className="w-full"
                    />
                </div>
            );
        }
    );

    // Define common header styling for reuse
    const headerCellStyle =
        'text-center py-2 px-2 font-medium text-gray-600 font-[Inter] font-semibold text-[12px] leading-4 align-middle';

    // Define columns for the Summary Table - memoized to prevent unnecessary recalculations
    const billingColumns = useMemo<ColumnDefinition<SummaryData>[]>(
        () => [
            {
                key: 'appointmentsCompleted',
                header: 'Appointments completed',
                width: '12%',
                renderCell: (item) => (
                    <BlackHeaderCellTable data={item.appointmentsCompleted} />
                ),
                headCellClassName: `${headerCellStyle} !border-b-[#E0E0E0]`,
                cellClassName: '!py-0 !px-0 !border-b-none',
            },
            {
                key: 'invoicesGenerated',
                header: 'Invoices Generated',
                width: '12%',
                renderCell: (item) => (
                    <BlackHeaderCellTable data={item.invoicesGenerated} />
                ),
                headCellClassName: `${headerCellStyle} !border-b-[#E0E0E0]`,
                cellClassName: '!py-0 !px-0 !border-b-none',
            },
            {
                key: 'totalBilling',
                header: 'Total Billing',
                width: '24%',
                renderCell: (item) => (
                    <BlackHeaderCellTable
                        data={formatCurrency(item.totalBilling)}
                    />
                ),
                headCellClassName: `${headerCellStyle} !border-b-[#E0E0E0]`,
                cellClassName: '!py-0 !px-0 !border-b-none',
            },
            {
                key: 'amountCollected',
                header: 'Amount Collected',
                width: '52%',
                renderCell: (item) => (
                    <PaymentBreakdown data={item.amountCollected} />
                ),
                headCellClassName: `${headerCellStyle} !border-b-[#E0E0E0]`,
                cellClassName:
                    '!py-0 !px-0 !first:rounded-b-none !last:rounded-b-none',
            },
        ],
        [headerCellStyle]
    );

    const refundColumns = useMemo<ColumnDefinition<SummaryData>[]>(
        () => [
            {
                key: 'blankCell',
                header: '',
                width: '12%',
                renderCell: (item) => (
                    <BlackHeaderCellTable data={item.appointmentsCompleted} />
                ),
                headCellClassName: `${headerCellStyle} !border-b-[#E0E0E0]`,
                cellClassName: '!py-0 !px-0 !border-b-none',
            },
            {
                key: 'creditNotesGenerated',
                header: 'Credit Notes Generated',
                width: '12%',
                renderCell: (item) => (
                    <BlackHeaderCellTable data={item.creditNotesGenerated} />
                ),
                headCellClassName: `${headerCellStyle} !border-b-[#E0E0E0]`,
                cellClassName: '!py-0 !px-0 !border-b-none',
            },
            {
                key: 'totalCreditNotes',
                header: 'Total Refund',
                width: '24%',
                renderCell: (item) => (
                    <BlackHeaderCellTable
                        data={formatCurrency(item.totalCreditNotes)}
                    />
                ),
                headCellClassName: `${headerCellStyle} !border-b-[#E0E0E0]`,
                cellClassName: '!py-0 !px-0',
            },
            {
                key: 'amountRefunded',
                header: 'Amount Returned',
                width: '52%', // Wider to accommodate the breakdown
                renderCell: (item) => (
                    <PaymentBreakdown data={item.amountRefunded} />
                ),
                headCellClassName: `${headerCellStyle} !border-b-[#E0E0E0]`,
                cellClassName: '!py-0 !px-0',
            },
        ],
        [headerCellStyle]
    );

    const netAmountColumns = useMemo<ColumnDefinition<SummaryData>[]>(
        () => [
            {
                key: 'blankCell',
                header: '',
                width: '48%',
                renderCell: () => (
                    <BlackHeaderCellTable
                        data={null}
                        bgColor="!bg-neutral-50"
                    />
                ),
                headCellClassName: `${headerCellStyle} !rounded-t-none !border-b-[#E0E0E0]`,
                cellClassName: '!py-0 !px-0 !border-t-none',
            },
            {
                key: 'netAmount',
                header: 'Net Amount',
                width: '52%',
                renderCell: (item) => (
                    <PaymentBreakdown
                        data={calculateNetAmount(item)}
                        bgColour={'!bg-primary-100'}
                    />
                ),
                headCellClassName: `${headerCellStyle} !rounded-t-none !border-b-[#E0E0E0] !bg-primary-100`,
                cellClassName: '!py-0 !px-0 !border-b-[#E0E0E0]',
            },
        ],
        [headerCellStyle]
    );

    // Check if we should show the custom date range picker
    const showCustomDatePicker = timeTab === 'Custom';

    // Check if we're waiting for end date in custom mode
    const waitingForEndDate =
        timeTab === 'Custom' &&
        customDateRange.startDate &&
        !customDateRange.endDate;

    return (
        <div className="bg-gray-50 rounded-xl p-4 py-4">
            <div className="flex justify-between gap-3 mx-2 px-2 border-b border-primary-50">
                <div className="flex items-center justify-between mb-2">
                    <h2 className="text-2xl font-[Inter] font-semibold leading-4 align-middle">
                        Summary
                    </h2>
                </div>
            </div>

            <div className="flex items-center w-full m-2 pr-4">
                <HorizontalTabs
                    onTabChanged={handleTimeTabChange}
                    highlight="fill"
                    size="s"
                    color="default"
                    activeTab={timeTab}
                    tabItems={timeTabItems}
                    ariaLabel="Summary time period"
                    className="font-[Inter] font-semibold text-xs leading-4 align-middle"
                    variant="secondary"
                />
                {showCustomDatePicker && (
                    <div className="ml-2 w-[400px]">
                        <RangeDatePicker
                            id="summary-date-range"
                            name="summary-date-range"
                            dateFormat="yyyy-MM-dd"
                            onDateChange={handleCustomDateChange}
                            startDateValue={
                                customDateRange.startDate?.toISOString() || ''
                            }
                            endDateValue={
                                customDateRange.endDate?.toISOString() || ''
                            }
                            placeholderStart="Start Date"
                            placeholderEnd="End Date"
                            containerClass="w-full"
                            shouldCloseOnSelect={true}
                        />
                    </div>
                )}
                <div className="flex-grow" />
                <Button
                    className="flex items-center justify-center"
                    onClick={() => setIsShareModal(true)}
                    id="share-analytics"
                    onlyIcon
                    type="button"
                    variant="secondary"
                    size="extraSmall"
                    disabled={isShareLoading}
                >
                    <Share
                        className="flex items-center justify-center"
                        size={16}
                    />
                </Button>
            </div>

            <div className="w-full">
                {isLoading ? (
                    <div className="h-[300px] ml-2 rounded-md bg-gray-100 flex items-center justify-center">
                        Loading...
                    </div>
                ) : waitingForEndDate ? (
                    <div className="h-[300px] ml-2 rounded-md bg-gray-100 flex flex-col items-center justify-center gap-2">
                        <Text variant="body" textColor="text-neutral-600">
                            Select End Date
                        </Text>
                        <Text variant="caption" textColor="text-neutral-400">
                            Please select an end date to view the data
                        </Text>
                    </div>
                ) : summaryData ? (
                    <div className="mt-4">
                        {/* Billing and Amount Collected */}
                        <Table<any>
                            columns={billingColumns}
                            data={tableData}
                            variant="default"
                            emptyTableMessage="No data available"
                            className="!rounded-b-none !border-b-0"
                        />
                        <div className="border-t border-primary-700"></div>
                        {/* Credit Notes and Amount Refunded */}
                        <Table<any>
                            columns={refundColumns}
                            data={tableData}
                            variant="borderless"
                            emptyTableMessage="No data available"
                        />
                        <div className="border-t border-primary-700"></div>
                        {/* Net Amount */}
                        <Table<any>
                            columns={netAmountColumns}
                            data={tableData}
                            variant="default"
                            emptyTableMessage="No data available"
                            className="!rounded-t-none !border-b-0"
                        />
                    </div>
                ) : (
                    <div className="h-[300px] ml-2 rounded-md bg-gray-100 flex items-center justify-center">
                        No data available
                    </div>
                )}
            </div>

            {/* Share Analytics Modal */}
            {isShareModal && (
                <ShareAnalyticsModal
                    isOpen={isShareModal}
                    onClose={() => setIsShareModal(false)}
                    handleCancel={() => setIsShareModal(false)}
                    handleShare={handleShareAnalytics}
                    title="Share Analytics Documents"
                    isLoading={isShareLoading}
                />
            )}
        </div>
    );
};

export default Summary;
