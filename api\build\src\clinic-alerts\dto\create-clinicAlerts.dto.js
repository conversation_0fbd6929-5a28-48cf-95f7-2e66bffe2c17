"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateClinicAlertDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateClinicAlertDto {
}
exports.CreateClinicAlertDto = CreateClinicAlertDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique identifier for the clinic',
        example: '9c1aaa0e-847c-4bca-9da0-72960eaa269d'
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateClinicAlertDto.prototype, "clinicId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'name of the alert for creating clinic-alert',
        example: 'Aggressive'
    }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateClinicAlertDto.prototype, "alertName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'severity of the alert for creating patient-alert',
        example: 'Moderate'
    }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateClinicAlertDto.prototype, "severity", void 0);
//# sourceMappingURL=create-clinicAlerts.dto.js.map